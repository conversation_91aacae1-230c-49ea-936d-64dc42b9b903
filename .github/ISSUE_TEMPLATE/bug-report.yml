name: "\U0001F41B Bug Report"
description: Problems and issues with code in PXF for CloudberryDB.
title: "[Bug] "
labels: ["type: Bug"]
body:
  - type: markdown
    attributes:
      value: "Thank you for reporting the problem! We really appreciate your efforts to improve Cloudberry Database. Before reporting it, please (ideally) test on the latest release or main to see if the issue is fixed."
  - type: textarea
    attributes:
      label: Cloudberry Database and PXF version
      description: What version are you using? 
  - type: textarea
    attributes:
      label: What happened
      description: Describe what happened.
      placeholder: >
        Please provide the context in which the problem occurred and explain what happened
    validations:
      required: true
  - type: textarea
    attributes:
      label: What you think should happen instead
      description: What do you think went wrong?
      placeholder: >
        Please explain why you think the behavior is erroneous. It is extremely helpful if you copy&paste the fragment of logs showing the exact error messages or wrong behavior. If you can provide some screenshots or videos, you can include files by dragging and dropping them here.
  - type: textarea
    attributes:
      label: How to reproduce
      description: >
        What should we do to reproduce the problem? Any SQL commands or operations? 
      placeholder: >
        Please make sure you provide a reproducible step-by-step case of how to reproduce the problem as minimally and precisely as possible. Remember that non-reproducible issues will be closed! Opening a discussion is recommended as a first step.
  - type: input
    attributes:
      label: Operating System
      description: What Operating System are you using?
      placeholder: "You can get it via `cat /etc/os-release` for example"
  - type: textarea
    attributes:
      label: Anything else
      description: Anything else we need to know?
      placeholder: >
        How often does this problem occur? (Once? Every time? Only when certain conditions are met?) Any relevant logs to include? Put them here.
  - type: checkboxes
    attributes:
      label: Are you willing to submit PR?
      description: >
        This is absolutely not required, but we are happy to guide you in the contribution process, especially if you already understand how to implement the fix.
      options:
        - label: Yes, I am willing to submit a PR!
  - type: checkboxes
    attributes:
      label: Code of Conduct
      description: The Code of Conduct helps create a safe space for everyone. We require that everyone agrees to it.
      options:
        - label: >
            I agree to follow this project's
            [Code of Conduct](https://cloudberrydb.org/community/coc).
          required: true
  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
