<!--Thank you for contributing! -->
<!--In case of an existing issue or discussions, please reference it-->
fix #ISSUE_Number
<!--Remove this section if no corresponding issue.-->

---

## Change logs

> Describe your change clearly, including what problem is being solved or what document is being added or updated.

## Contributor's checklist

Here are some reminders before you submit your pull request:

* Make sure that your Pull Request has a clear title and commit message. You can take the [Git commit template](https://github.com/cloudberrydb/cloudberrydb/blob/main/.gitmessage) as a reference.
* Sign the Contributor License Agreement as prompted for your first-time contribution (*One-time setup*).
* Learn the [code contribution](https://cloudberrydb.org/contribute/code) and [doc contribution](https://cloudberrydb.org/contribute/doc) guides for better collaboration.
* List your communications in the [GitHub Issues](https://github.com/cloudberrydb/cloudberrydb-site/issues) or [Discussions](https://github.com/orgs/cloudberrydb/discussions) (if has or needed).
* Feel free to ask for the cloudberrydb team to help review and approve.
