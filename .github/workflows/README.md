# CloudBerry PXF CI/CD Infrastructure

This directory contains the GitHub Actions workflows for the CloudBerry PXF project, providing comprehensive CI/CD capabilities that replace the previous Concourse-based system.

## Workflow Architecture

The CI/CD system is implemented as three modular workflows, following enterprise-grade practices:

### 1. Main CI Pipeline (`ci.yml`)
**Trigger**: Push/PR to main branches, manual dispatch
**Purpose**: Core development workflow for continuous integration

**Components**:
- **Code Quality & Security**: Linting, basic security checks, license validation
- **Build Matrix**: Parallel builds for external-table, CLI, and server components
- **Unit Tests**: Component-specific testing (Java/Gradle, Go/Ginkgo, C/Check)
- **Integration Tests**: Multi-Hadoop version testing (HDP2, HDP3, CDH)
- **Packaging**: TAR, RPM, DEB package creation for multiple architectures
- **Performance Tests**: Basic performance benchmarks
- **Staging Deployment**: Automated staging environment deployment

**Key Features**:
- Multi-platform support (Linux x86_64/ARM64, macOS)
- Matrix builds for component isolation
- Comprehensive artifact management
- Smart caching for dependencies
- Parallel execution optimization

### 2. Advanced Testing (`advanced-testing.yml`)
**Trigger**: Manual dispatch only
**Purpose**: Extended testing scenarios for stability and compatibility

**Test Types**:
- **Longevity Tests**: 24-hour stability testing across Hadoop versions
- **Stress Tests**: High-throughput, high-concurrency, large-dataset scenarios
- **Multi-Version Compatibility**: GPDB/PostgreSQL version matrix testing
- **Certification Tests**: Platform certification (CentOS, Ubuntu) with vendor Hadoop
- **Cloud Platform Tests**: AWS EMR, GCP Dataproc, Azure HDInsight validation
- **Performance Regression**: Automated performance comparison against baseline

### 3. Security & Compliance (`security.yml`)
**Trigger**: Push/PR to main branch, manual dispatch
**Purpose**: Enterprise security and compliance validation

**Security Scanning**:
- **Dependency Vulnerabilities**: OWASP, govulncheck, Nancy for all components
- **Static Code Analysis**: CodeQL, SpotBugs, Gosec, Flawfinder
- **Container Security**: Trivy, Snyk, Docker Bench Security
- **License Compliance**: FOSSA, go-licenses, header validation
- **Secrets Scanning**: TruffleHog, detect-secrets
- **Policy Compliance**: Security policy validation, Dockerfile best practices

## Technology Stack Integration

### Java (Server Components)
- **Build Tool**: Gradle with parallel execution
- **Testing**: JUnit with Jacoco coverage
- **Security**: SpotBugs, OWASP dependency check
- **Documentation**: JavaDoc generation

### Go (CLI Component)
- **Build Tool**: Native Go toolchain
- **Testing**: Ginkgo BDD framework
- **Security**: govulncheck, Gosec, go-licenses
- **Cross-compilation**: Multi-platform binary generation

### C (External Table Extension)
- **Build Tool**: PostgreSQL PGXS system
- **Testing**: Check framework
- **Security**: Cppcheck, Flawfinder
- **Dependencies**: libcurl, PostgreSQL development headers

### Hadoop Ecosystem Support
- **Distributions**: HDP2, HDP3, CDH, MapR
- **Cloud Platforms**: AWS EMR, GCP Dataproc, Azure HDInsight
- **Testing**: Automated cluster provisioning and testing

## Workflow Features

### Performance Optimizations
- **Parallel Execution**: Maximum tool parallelization
- **Smart Caching**: Gradle, Go modules, system dependencies
- **Artifact Reuse**: Build once, test everywhere approach
- **Skip Duplicate Actions**: Prevents redundant builds

### Enterprise Features
- **Multi-Environment**: Development, staging, production promotion
- **Approval Gates**: Production deployment controls
- **Audit Trail**: Comprehensive logging and artifact retention
- **Rollback Capability**: Safe deployment rollback procedures

### Quality Gates
- **Code Coverage**: Minimum coverage thresholds
- **Security Thresholds**: Vulnerability severity limits
- **Performance Benchmarks**: Regression detection
- **License Compliance**: Automated license validation

## Usage

### Development Workflow
1. **Push/PR**: Triggers main CI pipeline
2. **Code Review**: Automated quality checks
3. **Merge**: Full integration testing
4. **Deploy**: Automated staging deployment

### Release Workflow
**Note**: Apache CloudBerry PXF follows Apache Software Foundation release processes. For official releases, please refer to the Apache release guidelines and procedures.

1. **Development**: Continue with standard development workflow
2. **Apache Release Process**: Follow ASF release procedures
3. **Community Validation**: Apache community review and voting
4. **Official Release**: ASF release management

### Security Monitoring
- **Manual Scans**: Security scans triggered manually or on push/PR
- **Compliance Reports**: Manual compliance validation
- **Alert System**: Security issue notifications

## Configuration

### Environment Variables
```bash
PXF_VERSION=1.0.0
JAVA_VERSION=8
GO_VERSION=1.19
```

### Required Secrets
- `GITHUB_TOKEN`: GitHub API access
- `SNYK_TOKEN`: Snyk security scanning
- Cloud provider credentials for cloud testing

### Repository Settings
- Branch protection rules for main branches
- Required status checks for all workflows
- Deployment environment configurations

## Migration from Concourse

This GitHub Actions setup provides feature parity with the previous Concourse CI system:

### Maintained Capabilities
- ✅ Singlecluster testing
- ✅ Certification testing
- ✅ Longevity testing
- ✅ Multi-Hadoop support
- ✅ Package generation (TAR, RPM, DEB)
- ✅ Performance testing
- ✅ Security scanning

### Enhanced Capabilities
- 🚀 Multi-platform builds (ARM64 support)
- 🚀 Container image generation
- 🚀 Advanced security scanning
- 🚀 License compliance automation
- 🚀 Cloud platform testing
- 🚀 Performance regression detection
- 🚀 Manual trigger controls

## Monitoring and Maintenance

### Workflow Health
- Monitor workflow execution times
- Track success/failure rates
- Review artifact storage usage

### Security Updates
- Regular dependency updates
- Security tool version updates
- Compliance policy reviews

### Performance Optimization
- Cache hit ratio monitoring
- Parallel execution efficiency
- Resource utilization tracking

## Troubleshooting

### Common Issues
1. **Build Failures**: Check component-specific logs
2. **Test Timeouts**: Adjust timeout values for long-running tests
3. **Artifact Upload**: Verify artifact size limits
4. **Security Scans**: Review vulnerability thresholds

### Debug Mode
Enable workflow debug logging:
```yaml
env:
  ACTIONS_RUNNER_DEBUG: true
  ACTIONS_STEP_DEBUG: true
```

## Contributing

When modifying workflows:
1. Test changes in feature branches
2. Validate workflow syntax
3. Review security implications
4. Update documentation
5. Consider backward compatibility

## Support

For CI/CD issues:
- Check workflow logs in GitHub Actions tab
- Review artifact outputs
- Consult security scan reports
- Reference original Concourse configurations for migration guidance