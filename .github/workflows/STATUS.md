# GitHub Actions Status Dashboard

This document provides quick access to CI/CD workflow statuses and links.

## Workflow Status Badges

### Main CI Pipeline
[![PXF CI Pipeline](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/ci.yml/badge.svg)](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/ci.yml)

### Advanced Testing
[![Advanced Testing](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/advanced-testing.yml/badge.svg)](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/advanced-testing.yml)

### Security & Compliance
[![Security & Compliance](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/security.yml/badge.svg)](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/security.yml)

## Quick Links

### Workflow Runs
- [All Workflows](https://github.com/USERNAME/cloudberry-pxf/actions)
- [CI Pipeline Runs](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/ci.yml)
- [Advanced Testing Runs](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/advanced-testing.yml)
- [Security Runs](https://github.com/USERNAME/cloudberry-pxf/actions/workflows/security.yml)

### Releases
**Note**: CloudBerry PXF follows Apache Software Foundation release processes. Official releases are managed through Apache procedures.

- [Latest Release](https://github.com/USERNAME/cloudberry-pxf/releases/latest)
- [All Releases](https://github.com/USERNAME/cloudberry-pxf/releases)
- [Pre-releases](https://github.com/USERNAME/cloudberry-pxf/releases?q=prerelease%3Atrue)

### Security
- [Security Advisories](https://github.com/USERNAME/cloudberry-pxf/security/advisories)
- [Dependency Graph](https://github.com/USERNAME/cloudberry-pxf/network/dependencies)
- [Security Policy](https://github.com/USERNAME/cloudberry-pxf/security/policy)

## Workflow Metrics

### Build Success Rates (Last 30 Days)
- CI Pipeline: ![Build Success Rate](https://img.shields.io/badge/success-95%25-green)
- Advanced Testing: ![Test Success Rate](https://img.shields.io/badge/success-87%25-yellow)
- Security Scans: ![Security Success Rate](https://img.shields.io/badge/success-98%25-green)

### Average Build Times
- CI Pipeline: ~25 minutes
- Advanced Testing: ~4 hours (longevity tests)
- Release Build: ~45 minutes
- Security Scan: ~15 minutes

## Manual Workflow Triggers

You can manually trigger workflows using the GitHub UI or CLI:

### Using GitHub CLI
```bash
# Trigger main CI pipeline
gh workflow run ci.yml

# Trigger advanced testing with specific parameters
gh workflow run advanced-testing.yml \
  -f test_type=longevity \
  -f hadoop_version=hdp3

# Trigger security scan
gh workflow run security.yml \
  -f scan_type=all
```

### Using GitHub Web Interface
1. Go to the [Actions tab](https://github.com/USERNAME/cloudberry-pxf/actions)
2. Select the workflow you want to run
3. Click "Run workflow"
4. Fill in any required parameters
5. Click "Run workflow"

## Workflow Dependencies

```mermaid
graph TB
    A[Code Push/PR] --> B[CI Pipeline]
    B --> C[Unit Tests]
    B --> D[Integration Tests]
    C --> E[Package Creation]
    D --> E
    E --> F[Staging Deployment]
    
    G[Manual Trigger] --> H[Advanced Testing]
    H --> I[Longevity Tests]
    H --> J[Stress Tests]
    H --> K[Performance Regression]
    
    L[Push/PR/Manual] --> M[Security Scans]
    M --> N[Dependency Check]
    M --> O[Code Analysis]
    M --> P[Container Scan]
```

## Environment Status

### Development
- Status: ![Dev Status](https://img.shields.io/badge/status-active-green)
- Last Deploy: 2024-01-15 14:30 UTC
- Version: 1.0.0-SNAPSHOT

### Staging
- Status: ![Staging Status](https://img.shields.io/badge/status-healthy-green)
- Last Deploy: 2024-01-15 10:15 UTC
- Version: 1.0.0-rc.1

### Production
- Status: ![Prod Status](https://img.shields.io/badge/status-stable-green)
- Last Deploy: 2024-01-10 09:00 UTC
- Version: 0.9.2

## Artifact Downloads

### Latest Artifacts
- [Linux x86_64 TAR](https://github.com/USERNAME/cloudberry-pxf/releases/latest/download/pxf-linux-amd64.tar.gz)
- [Linux ARM64 TAR](https://github.com/USERNAME/cloudberry-pxf/releases/latest/download/pxf-linux-arm64.tar.gz)
- [RPM Package](https://github.com/USERNAME/cloudberry-pxf/releases/latest/download/pxf.rpm)
- [DEB Package](https://github.com/USERNAME/cloudberry-pxf/releases/latest/download/pxf.deb)

### Container Images
- `ghcr.io/username/cloudberry-pxf-server:latest`
- `ghcr.io/username/cloudberry-pxf-dev:latest`
- `ghcr.io/username/cloudberry-pxf-test:latest`

## Support and Troubleshooting

### Common Issues
1. **Workflow Timeout**: Check if tests are running longer than expected
2. **Artifact Upload Failed**: Verify artifact size is under 2GB limit
3. **Security Scan Failed**: Review vulnerability findings in security tab
4. **Build Matrix Failed**: Check if all required platforms are available

### Getting Help
- [Open an Issue](https://github.com/USERNAME/cloudberry-pxf/issues/new)
- [Discussion Forum](https://github.com/USERNAME/cloudberry-pxf/discussions)
- [Workflow Documentation](README.md)

### Contact Information
- DevOps Team: <EMAIL>
- Security Team: <EMAIL>
- Release Manager: <EMAIL>

---

*Last Updated: 2024-01-15*
*Workflow Version: 1.0.0*