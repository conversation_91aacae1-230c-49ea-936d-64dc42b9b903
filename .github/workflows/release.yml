name: Release Management

on:
  push:
    tags:
      - 'v*.*.*'
      - 'release-*'
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Type of release'
        required: true
        default: 'patch'
        type: choice
        options:
          - major
          - minor
          - patch
          - rc
          - snapshot
      release_notes:
        description: 'Release notes'
        required: false
        type: string

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # ======================================================================
  # RELEASE VALIDATION
  # ======================================================================
  
  validate-release:
    name: Validate Release
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      is_prerelease: ${{ steps.version.outputs.is_prerelease }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Determine release version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "push" && "${{ github.ref }}" == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          else
            # For workflow_dispatch, generate version based on type
            CURRENT_VERSION=$(cat version)
            case "${{ github.event.inputs.release_type }}" in
              major)
                VERSION=$(echo $CURRENT_VERSION | awk -F. '{print ($1+1)".0.0"}')
                ;;
              minor)
                VERSION=$(echo $CURRENT_VERSION | awk -F. '{print $1"."($2+1)".0"}')
                ;;
              patch)
                VERSION=$(echo $CURRENT_VERSION | awk -F. '{print $1"."$2"."($3+1)}')
                ;;
              rc)
                VERSION="$CURRENT_VERSION-rc.$(date +%Y%m%d%H%M%S)"
                ;;
              snapshot)
                VERSION="$CURRENT_VERSION-SNAPSHOT"
                ;;
            esac
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          
          # Determine if this is a prerelease
          if [[ "$VERSION" == *"-rc"* ]] || [[ "$VERSION" == *"-SNAPSHOT"* ]] || [[ "$VERSION" == *"-alpha"* ]] || [[ "$VERSION" == *"-beta"* ]]; then
            echo "is_prerelease=true" >> $GITHUB_OUTPUT
          else
            echo "is_prerelease=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Validate version format
        run: |
          VERSION="${{ steps.version.outputs.version }}"
          if [[ ! "$VERSION" =~ ^v?[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9.-]+)?$ ]]; then
            echo "Invalid version format: $VERSION"
            exit 1
          fi
          
      - name: Check for required files
        run: |
          required_files=("CHANGELOG.md" "README.md" "LICENSE" "version")
          for file in "${required_files[@]}"; do
            if [[ ! -f "$file" ]]; then
              echo "Required file missing: $file"
              exit 1
            fi
          done

  # ======================================================================
  # BUILD RELEASE ARTIFACTS
  # ======================================================================
  
  build-release-artifacts:
    name: Build Release Artifacts
    runs-on: ubuntu-latest
    needs: validate-release
    strategy:
      matrix:
        platform: [linux/amd64, linux/arm64, darwin/amd64, darwin/arm64]
        package-type: [tar, rpm, deb]
        exclude:
          # RPM and DEB packages only for Linux
          - platform: darwin/amd64
            package-type: rpm
          - platform: darwin/amd64
            package-type: deb
          - platform: darwin/arm64
            package-type: rpm
          - platform: darwin/arm64
            package-type: deb
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up build environment
        run: |
          # Extract platform information
          PLATFORM="${{ matrix.platform }}"
          OS=${PLATFORM%/*}
          ARCH=${PLATFORM#*/}
          
          echo "GOOS=$OS" >> $GITHUB_ENV
          echo "GOARCH=$ARCH" >> $GITHUB_ENV
          
      - name: Set up QEMU for cross-compilation
        if: matrix.platform != 'linux/amd64'
        uses: docker/setup-qemu-action@v3
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Install build dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            build-essential \
            libcurl4-openssl-dev \
            postgresql-server-dev-all \
            postgresql-client \
            make \
            gcc \
            rpm \
            dpkg-dev \
            cross-build-essential-arm64
            
      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '8'
          
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.19'
          
      - name: Update version
        run: |
          echo "${{ needs.validate-release.outputs.version }}" > version
          
      - name: Build for ${{ matrix.platform }}
        run: |
          export PXF_VERSION="${{ needs.validate-release.outputs.version }}"
          export GOOS="${{ env.GOOS }}"
          export GOARCH="${{ env.GOARCH }}"
          
          # Cross-compile for different platforms
          if [[ "${{ matrix.platform }}" == "linux/amd64" ]]; then
            make all
          else
            # For cross-compilation, build components separately
            make external-table
            make cli
            make server
          fi
          
      - name: Create ${{ matrix.package-type }} package
        if: matrix.package-type != 'tar' || startsWith(matrix.platform, 'linux')
        run: |
          export PXF_VERSION="${{ needs.validate-release.outputs.version }}"
          case "${{ matrix.package-type }}" in
            tar)
              make tar
              ;;
            rpm)
              if [[ "${{ matrix.platform }}" == "linux/amd64" ]]; then
                make rpm
              fi
              ;;
            deb)
              if [[ "${{ matrix.platform }}" == "linux/amd64" ]]; then
                make deb
              fi
              ;;
          esac
          
      - name: Generate checksums
        run: |
          cd build
          find . -name "*.tar.gz" -o -name "*.rpm" -o -name "*.deb" | while read file; do
            sha256sum "$file" > "$file.sha256"
            md5sum "$file" > "$file.md5"
          done
          
      - name: Upload release artifacts
        uses: actions/upload-artifact@v3
        with:
          name: release-artifacts-${{ matrix.platform }}-${{ matrix.package-type }}
          path: |
            build/dist/
            build/rpmbuild/RPMS/
            build/*.deb
            build/**/*.sha256
            build/**/*.md5
          retention-days: 90

  # ======================================================================
  # CONTAINER IMAGES
  # ======================================================================
  
  build-container-images:
    name: Build Container Images
    runs-on: ubuntu-latest
    needs: validate-release
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        image-type: [pxf-server, pxf-dev, pxf-test]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.image-type }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=raw,value=${{ needs.validate-release.outputs.version }}
            
      - name: Build and push container image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: concourse/docker/${{ matrix.image-type }}/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            PXF_VERSION=${{ needs.validate-release.outputs.version }}
            
      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.image-type }}:${{ needs.validate-release.outputs.version }}
          format: spdx-json
          output-file: sbom-${{ matrix.image-type }}.spdx.json
          
      - name: Upload SBOM
        uses: actions/upload-artifact@v3
        with:
          name: sbom-${{ matrix.image-type }}
          path: sbom-${{ matrix.image-type }}.spdx.json
          retention-days: 90

  # ======================================================================
  # RELEASE TESTING
  # ======================================================================
  
  release-testing:
    name: Release Testing
    runs-on: ubuntu-latest
    needs: [build-release-artifacts, build-container-images]
    strategy:
      matrix:
        test-type: [smoke, integration, upgrade]
        platform: [centos7, ubuntu18]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download release artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts/
          
      - name: Set up test environment
        run: |
          echo "Setting up ${{ matrix.platform }} test environment"
          
      - name: Install release packages
        run: |
          # Install the built packages for testing
          case "${{ matrix.platform }}" in
            centos7)
              sudo rpm -i artifacts/release-artifacts-linux-amd64-rpm/*.rpm
              ;;
            ubuntu18)
              sudo dpkg -i artifacts/release-artifacts-linux-amd64-deb/*.deb
              ;;
          esac
          
      - name: Run ${{ matrix.test-type }} tests
        run: |
          cd automation
          export RELEASE_TESTING=true
          export TEST_TYPE=${{ matrix.test-type }}
          make release-${{ matrix.test-type }}-test
          
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: release-test-results-${{ matrix.test-type }}-${{ matrix.platform }}
          path: automation/release-test-results/
          retention-days: 30

  # ======================================================================
  # DOCUMENTATION GENERATION
  # ======================================================================
  
  generate-documentation:
    name: Generate Release Documentation
    runs-on: ubuntu-latest
    needs: validate-release
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '8'
          
      - name: Generate API documentation
        run: |
          cd server
          ./gradlew aggregateJavadoc
          
      - name: Generate user documentation
        run: |
          # Generate user-facing documentation
          mkdir -p docs/release
          cp README.md docs/release/
          cp CHANGELOG.md docs/release/
          cp -r server/build/docs/javadoc docs/release/api-docs
          
      - name: Generate release notes
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          
          echo "# Release Notes - $VERSION" > docs/release/RELEASE_NOTES.md
          echo "" >> docs/release/RELEASE_NOTES.md
          echo "Release Date: $(date +%Y-%m-%d)" >> docs/release/RELEASE_NOTES.md
          echo "" >> docs/release/RELEASE_NOTES.md
          
          if [[ -n "${{ github.event.inputs.release_notes }}" ]]; then
            echo "${{ github.event.inputs.release_notes }}" >> docs/release/RELEASE_NOTES.md
          else
            # Extract changes from CHANGELOG.md
            awk "/^## \[$VERSION\]/,/^## \[/" CHANGELOG.md | head -n -1 >> docs/release/RELEASE_NOTES.md
          fi
          
      - name: Upload documentation
        uses: actions/upload-artifact@v3
        with:
          name: release-documentation
          path: docs/release/
          retention-days: 90

  # ======================================================================
  # CREATE GITHUB RELEASE
  # ======================================================================
  
  create-github-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [validate-release, build-release-artifacts, release-testing, generate-documentation]
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          path: release-assets/
          
      - name: Prepare release assets
        run: |
          mkdir -p final-assets
          
          # Collect all package files
          find release-assets/ -name "*.tar.gz" -o -name "*.rpm" -o -name "*.deb" | while read file; do
            cp "$file" final-assets/
          done
          
          # Collect checksums
          find release-assets/ -name "*.sha256" -o -name "*.md5" | while read file; do
            cp "$file" final-assets/
          done
          
          # Copy documentation
          cp -r release-assets/release-documentation/* final-assets/
          
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ needs.validate-release.outputs.version }}
          name: PXF ${{ needs.validate-release.outputs.version }}
          body_path: final-assets/RELEASE_NOTES.md
          prerelease: ${{ needs.validate-release.outputs.is_prerelease }}
          files: |
            final-assets/*.tar.gz
            final-assets/*.rpm
            final-assets/*.deb
            final-assets/*.sha256
            final-assets/*.md5
          generate_release_notes: true
          
  # ======================================================================
  # ARTIFACT PROMOTION
  # ======================================================================
  
  promote-artifacts:
    name: Promote Artifacts
    runs-on: ubuntu-latest
    needs: [create-github-release, validate-release]
    if: needs.validate-release.outputs.is_prerelease == 'false'
    steps:
      - name: Download release artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts/
          
      - name: Promote to stable repository
        run: |
          # Promote artifacts to stable/production repository
          echo "Promoting artifacts to stable repository"
          # This would typically involve uploading to package repositories
          
      - name: Update package indexes
        run: |
          # Update package repository indexes
          echo "Updating package repository indexes"
          
      - name: Notify distribution channels
        run: |
          # Notify various distribution channels about the new release
          echo "Notifying distribution channels"

  # ======================================================================
  # POST-RELEASE TASKS
  # ======================================================================
  
  post-release-tasks:
    name: Post-Release Tasks
    runs-on: ubuntu-latest
    needs: [create-github-release, validate-release]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Update version for next development cycle
        if: needs.validate-release.outputs.is_prerelease == 'false'
        run: |
          CURRENT_VERSION="${{ needs.validate-release.outputs.version }}"
          # Bump to next development version
          NEXT_VERSION=$(echo $CURRENT_VERSION | awk -F. '{print $1"."$2"."($3+1)"-SNAPSHOT"}')
          echo $NEXT_VERSION > version
          
          # Update server gradle.properties
          sed -i "s/version=.*/version=$NEXT_VERSION/" server/gradle.properties
          
      - name: Create post-release commit
        if: needs.validate-release.outputs.is_prerelease == 'false'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add version server/gradle.properties
          git commit -m "Bump version to next development cycle"
          git push
          
      - name: Create milestone for next release
        if: needs.validate-release.outputs.is_prerelease == 'false'
        run: |
          # Create GitHub milestone for next release
          echo "Creating milestone for next release"
          
      - name: Send release notifications
        run: |
          # Send notifications to stakeholders
          echo "Release ${{ needs.validate-release.outputs.version }} completed successfully"