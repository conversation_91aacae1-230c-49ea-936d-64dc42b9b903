name: Security & Compliance

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - dependency
          - code
          - container
          - license

env:
  JAVA_VERSION: "8"
  GO_VERSION: "1.19"

jobs:
  # ======================================================================
  # DEPENDENCY VULNERABILITY SCANNING
  # ======================================================================
  
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'dependency' || github.event.inputs.scan_type == 'all'
    strategy:
      matrix:
        component: [server, cli, external-table]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Java
        if: matrix.component == 'server'
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Set up Go
        if: matrix.component == 'cli'
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
          
      - name: Install C/C++ dependency scanner
        if: matrix.component == 'external-table'
        run: |
          # Install tools for C/C++ dependency scanning
          sudo apt-get update
          sudo apt-get install -y cppcheck
          
      - name: Java dependency scan (OWASP)
        if: matrix.component == 'server'
        run: |
          cd server
          ./gradlew dependencyCheckAnalyze
          
      - name: Go dependency scan (govulncheck)
        if: matrix.component == 'cli'
        run: |
          cd cli/go/src/pxf-cli
          go install golang.org/x/vuln/cmd/govulncheck@latest
          govulncheck ./...
          
      - name: Go mod security scan (Nancy)
        if: matrix.component == 'cli'
        run: |
          cd cli/go/src/pxf-cli
          go list -json -deps ./... | docker run --rm -i sonatypecommunity/nancy:latest sleuth
          
      - name: C/C++ dependency scan
        if: matrix.component == 'external-table'
        run: |
          cd external-table
          # Scan for common vulnerabilities in C code
          cppcheck --enable=all --xml --output-file=cppcheck-results.xml src/
          
      - name: Upload dependency scan results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: dependency-scan-${{ matrix.component }}
          path: |
            server/build/reports/dependency-check/
            cli/go/src/pxf-cli/govulncheck-results.json
            external-table/cppcheck-results.xml
          retention-days: 30

  # ======================================================================
  # STATIC CODE ANALYSIS (SAST)
  # ======================================================================
  
  code-security-scan:
    name: Static Code Security Analysis
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'code' || github.event.inputs.scan_type == 'all'
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      matrix:
        language: [java, go, cpp]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality
          
      - name: Set up build environment
        run: |
          if [[ "${{ matrix.language }}" == "java" ]]; then
            sudo apt-get update
            sudo apt-get install -y openjdk-8-jdk
          elif [[ "${{ matrix.language }}" == "go" ]]; then
            curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.54.2
          elif [[ "${{ matrix.language }}" == "cpp" ]]; then
            sudo apt-get update
            sudo apt-get install -y build-essential libcurl4-openssl-dev postgresql-server-dev-all
          fi
          
      - name: Build codebase
        run: |
          case "${{ matrix.language }}" in
            java)
              cd server && ./gradlew compileJava
              ;;
            go)
              cd cli/go/src/pxf-cli && go build
              ;;
            cpp)
              cd external-table && make
              ;;
          esac
          
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{ matrix.language }}"
          
      - name: Run additional security tools
        run: |
          case "${{ matrix.language }}" in
            java)
              cd server
              # SpotBugs security analysis
              ./gradlew spotbugsMain
              ;;
            go)
              cd cli/go/src/pxf-cli
              # Gosec security scanner
              go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
              gosec -fmt json -out gosec-results.json ./...
              ;;
            cpp)
              cd external-table
              # Flawfinder for C/C++
              pip install flawfinder
              flawfinder --html --output=flawfinder-results.html src/
              ;;
          esac
          
      - name: Upload SAST results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: sast-results-${{ matrix.language }}
          path: |
            server/build/reports/spotbugs/
            cli/go/src/pxf-cli/gosec-results.json
            external-table/flawfinder-results.html
          retention-days: 30

  # ======================================================================
  # CONTAINER SECURITY SCANNING
  # ======================================================================
  
  container-security-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'container' || github.event.inputs.scan_type == 'all'
    strategy:
      matrix:
        image: [pxf-dev-base, pxf-dev-server, pxf-build-base]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        
      - name: Build container image
        run: |
          cd concourse/docker/${{ matrix.image }}
          docker build -t test-${{ matrix.image }}:latest .
          
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: test-${{ matrix.image }}:latest
          format: 'sarif'
          output: 'trivy-${{ matrix.image }}.sarif'
          
      - name: Run Snyk container scan
        continue-on-error: true
        run: |
          npm install -g snyk
          snyk auth ${{ secrets.SNYK_TOKEN }}
          snyk container test test-${{ matrix.image }}:latest --file=concourse/docker/${{ matrix.image }}/Dockerfile --json > snyk-${{ matrix.image }}.json
          
      - name: Run Docker Bench Security
        run: |
          docker run --rm --net host --pid host --userns host --cap-add audit_control \
            -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
            -v /etc:/etc:ro \
            -v /usr/bin/containerd:/usr/bin/containerd:ro \
            -v /usr/bin/runc:/usr/bin/runc:ro \
            -v /usr/lib/systemd:/usr/lib/systemd:ro \
            -v /var/lib:/var/lib:ro \
            -v /var/run/docker.sock:/var/run/docker.sock:ro \
            --label docker_bench_security \
            docker/docker-bench-security > docker-bench-${{ matrix.image }}.txt
            
      - name: Upload container scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: trivy-${{ matrix.image }}.sarif
          
      - name: Upload container security artifacts
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: container-security-${{ matrix.image }}
          path: |
            trivy-${{ matrix.image }}.sarif
            snyk-${{ matrix.image }}.json
            docker-bench-${{ matrix.image }}.txt
          retention-days: 30

  # ======================================================================
  # LICENSE COMPLIANCE SCANNING
  # ======================================================================
  
  license-compliance:
    name: License Compliance Scan
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'license' || github.event.inputs.scan_type == 'all'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
          
      - name: Install license scanning tools
        run: |
          # Install FOSSA CLI for comprehensive license scanning
          curl -H 'Cache-Control: no-cache' https://raw.githubusercontent.com/fossas/fossa-cli/master/install-latest.sh | bash
          
          # Install license checker for Go
          go install github.com/google/go-licenses@latest
          
      - name: Java license scan
        run: |
          cd server
          # Use Gradle license plugin
          ./gradlew generateLicenseReport
          
          # Run FOSSA scan
          fossa analyze
          fossa test
          
      - name: Go license scan
        run: |
          cd cli/go/src/pxf-cli
          # Use go-licenses tool
          go-licenses report ./... > go-licenses-report.txt
          go-licenses check ./...
          
      - name: Check source file headers
        run: |
          # Check for proper license headers in source files
          python3 << 'EOF'
          import os
          import sys
          
          required_license_patterns = [
              "Licensed to the Apache Software Foundation",
              "Apache License, Version 2.0"
          ]
          
          violations = []
          extensions = ['.java', '.go', '.c', '.h', '.py', '.sh']
          exclude_dirs = ['.git', 'build', 'vendor', 'node_modules']
          
          for root, dirs, files in os.walk('.'):
              # Skip excluded directories
              dirs[:] = [d for d in dirs if d not in exclude_dirs]
              
              for file in files:
                  if any(file.endswith(ext) for ext in extensions):
                      filepath = os.path.join(root, file)
                      try:
                          with open(filepath, 'r', encoding='utf-8') as f:
                              content = f.read(1000)  # Read first 1000 chars
                              if not any(pattern in content for pattern in required_license_patterns):
                                  violations.append(filepath)
                      except:
                          continue
          
          if violations:
              print("Files missing proper license headers:")
              for violation in violations:
                  print(f"  - {violation}")
              sys.exit(1)
          else:
              print("All source files have proper license headers")
          EOF
          
      - name: Generate license compliance report
        run: |
          mkdir -p license-reports
          
          echo "# License Compliance Report" > license-reports/compliance-report.md
          echo "Generated on: $(date)" >> license-reports/compliance-report.md
          echo "" >> license-reports/compliance-report.md
          
          echo "## Java Dependencies" >> license-reports/compliance-report.md
          if [[ -f "server/build/reports/license/index.html" ]]; then
            echo "✅ Java license report generated" >> license-reports/compliance-report.md
          else
            echo "❌ Java license report failed" >> license-reports/compliance-report.md
          fi
          
          echo "## Go Dependencies" >> license-reports/compliance-report.md
          if [[ -f "cli/go/src/pxf-cli/go-licenses-report.txt" ]]; then
            echo "✅ Go license report generated" >> license-reports/compliance-report.md
          else
            echo "❌ Go license report failed" >> license-reports/compliance-report.md
          fi
          
      - name: Upload license compliance results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: license-compliance-report
          path: |
            license-reports/
            server/build/reports/license/
            cli/go/src/pxf-cli/go-licenses-report.txt
          retention-days: 90

  # ======================================================================
  # SECRETS SCANNING
  # ======================================================================
  
  secrets-scan:
    name: Secrets Scanning
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'all'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Install TruffleHog
        run: |
          curl -sSfL https://github.com/trufflesecurity/trufflehog/releases/download/v3.57.0/trufflehog_3.57.0_linux_amd64.tar.gz | tar -xzf -
          sudo mv trufflehog /usr/local/bin/
          
      - name: Run TruffleHog secrets scan
        run: |
          trufflehog git . --json > trufflehog-results.json
          
      - name: Install detect-secrets
        run: |
          pip install detect-secrets
          
      - name: Run detect-secrets baseline scan
        run: |
          detect-secrets scan --all-files --baseline .secrets.baseline
          
      - name: Check for secrets in configuration files
        run: |
          # Check for common secret patterns in config files
          grep -r -E "(password|secret|key|token)\s*[:=]\s*['\"][^'\"]*['\"]" . \
            --include="*.yml" --include="*.yaml" --include="*.properties" \
            --include="*.json" --include="*.xml" \
            --exclude-dir=".git" --exclude-dir="build" || true
            
      - name: Upload secrets scan results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: secrets-scan-results
          path: |
            trufflehog-results.json
            .secrets.baseline
          retention-days: 30

  # ======================================================================
  # SECURITY POLICY COMPLIANCE
  # ======================================================================
  
  security-policy-check:
    name: Security Policy Compliance
    runs-on: ubuntu-latest
    if: github.event.inputs.scan_type == 'all'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Check security policy files
        run: |
          required_files=("SECURITY.md" "CODE-OF-CONDUCT.md")
          missing_files=()
          
          for file in "${required_files[@]}"; do
            if [[ ! -f "$file" ]]; then
              missing_files+=("$file")
            fi
          done
          
          if [[ ${#missing_files[@]} -gt 0 ]]; then
            echo "Missing required security policy files:"
            printf '%s\n' "${missing_files[@]}"
            exit 1
          fi
          
      - name: Validate Dockerfile security practices
        run: |
          # Check Dockerfiles for security best practices
          find . -name "Dockerfile*" | while read dockerfile; do
            echo "Checking $dockerfile"
            
            # Check for running as root
            if ! grep -q "USER" "$dockerfile"; then
              echo "WARNING: $dockerfile doesn't specify a non-root USER"
            fi
            
            # Check for COPY instead of ADD
            if grep -q "^ADD" "$dockerfile"; then
              echo "WARNING: $dockerfile uses ADD instead of COPY"
            fi
            
            # Check for version pinning
            if grep -E "FROM.*:latest" "$dockerfile"; then
              echo "WARNING: $dockerfile uses 'latest' tag"
            fi
          done
          
      - name: Check for hardcoded credentials
        run: |
          # Additional check for hardcoded credentials
          suspicious_patterns=(
            "password\s*=\s*['\"][^'\"]*['\"]"
            "secret\s*=\s*['\"][^'\"]*['\"]"
            "api_key\s*=\s*['\"][^'\"]*['\"]"
            "private_key\s*=\s*['\"][^'\"]*['\"]"
          )
          
          for pattern in "${suspicious_patterns[@]}"; do
            if grep -r -E "$pattern" . --exclude-dir=".git" --exclude-dir="build"; then
              echo "WARNING: Found potential hardcoded credential: $pattern"
            fi
          done

  # ======================================================================
  # SECURITY REPORTING
  # ======================================================================
  
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-security-scan, container-security-scan, license-compliance, secrets-scan, security-policy-check]
    if: always()
    steps:
      - name: Download all security scan results
        uses: actions/download-artifact@v3
        with:
          path: security-results/
          
      - name: Generate comprehensive security report
        run: |
          mkdir -p reports
          
          cat > reports/security-summary.md << 'EOF'
          # Security & Compliance Summary Report
          
          Generated on: $(date)
          Commit: ${{ github.sha }}
          
          ## Scan Results Overview
          
          | Scan Type | Status | Details |
          |-----------|--------|---------|
          EOF
          
          # Check each scan result
          scans=("dependency-scan" "code-security-scan" "container-security-scan" "license-compliance" "secrets-scan")
          
          for scan in "${scans[@]}"; do
            if [[ -d "security-results/$scan"* ]]; then
              echo "| $scan | ✅ Completed | Results available |" >> reports/security-summary.md
            else
              echo "| $scan | ❌ Failed/Skipped | No results |" >> reports/security-summary.md
            fi
          done
          
          echo "" >> reports/security-summary.md
          echo "## Recommendations" >> reports/security-summary.md
          echo "" >> reports/security-summary.md
          echo "1. Review all HIGH and CRITICAL vulnerabilities" >> reports/security-summary.md
          echo "2. Update dependencies with known vulnerabilities" >> reports/security-summary.md
          echo "3. Address any license compliance issues" >> reports/security-summary.md
          echo "4. Remediate any secrets found in the codebase" >> reports/security-summary.md
          
      - name: Upload security summary report
        uses: actions/upload-artifact@v3
        with:
          name: security-summary-report
          path: reports/
          retention-days: 90
          
      - name: Create security issue if high vulnerabilities found
        if: failure()
        run: |
          echo "High-severity security issues detected. Consider creating a security issue."
          
      - name: Notify security team
        if: failure()
        run: |
          echo "Security scan failures detected. Security team should be notified."