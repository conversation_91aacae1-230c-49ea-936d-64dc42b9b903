-- @description query01 for HCatalog heterogeneous table with three text partitions
SELECT * FROM hcatalog.default.reg_heterogen_three_partitions ORDER BY fmt, t0;
  t0   |  t1  | num1 | d1 | fmt 
-------+------+------+----+-----
 row1  | s_6  |    1 |  6 | rc1
 row10 | s_15 |   10 | 15 | rc1
 row2  | s_7  |    2 |  7 | rc1
 row3  | s_8  |    3 |  8 | rc1
 row4  | s_9  |    4 |  9 | rc1
 row5  | s_10 |    5 | 10 | rc1
 row6  | s_11 |    6 | 11 | rc1
 row7  | s_12 |    7 | 12 | rc1
 row8  | s_13 |    8 | 13 | rc1
 row9  | s_14 |    9 | 14 | rc1
 row1  | s_6  |    1 |  6 | rc2
 row10 | s_15 |   10 | 15 | rc2
 row2  | s_7  |    2 |  7 | rc2
 row3  | s_8  |    3 |  8 | rc2
 row4  | s_9  |    4 |  9 | rc2
 row5  | s_10 |    5 | 10 | rc2
 row6  | s_11 |    6 | 11 | rc2
 row7  | s_12 |    7 | 12 | rc2
 row8  | s_13 |    8 | 13 | rc2
 row9  | s_14 |    9 | 14 | rc2
 row1  | s_6  |    1 |  6 | rc3
 row10 | s_15 |   10 | 15 | rc3
 row2  | s_7  |    2 |  7 | rc3
 row3  | s_8  |    3 |  8 | rc3
 row4  | s_9  |    4 |  9 | rc3
 row5  | s_10 |    5 | 10 | rc3
 row6  | s_11 |    6 | 11 | rc3
 row7  | s_12 |    7 | 12 | rc3
 row8  | s_13 |    8 | 13 | rc3
 row9  | s_14 |    9 | 14 | rc3
(30 rows)