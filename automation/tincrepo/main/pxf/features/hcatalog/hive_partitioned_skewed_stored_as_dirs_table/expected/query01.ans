-- @description query01 for HCatalog partitioned skewed and stored as directories table cases
SELECT * FROM hcatalog.default.hive_partitioned_skewed_stored_table ORDER BY fmt, t0;
  t0   |  t1  | num1 | d1 | fmt
-------+------+------+----+------
 row1  | s_6  |    1 |  6 | avro
 row10 | s_15 |   10 | 15 | avro
 row2  | s_7  |    2 |  7 | avro
 row3  | s_8  |    3 |  8 | avro
 row4  | s_9  |    4 |  9 | avro
 row5  | s_10 |    5 | 10 | avro
 row6  | s_11 |    6 | 11 | avro
 row7  | s_12 |    7 | 12 | avro
 row8  | s_13 |    8 | 13 | avro
 row9  | s_14 |    9 | 14 | avro
 row1  | s_6  |    1 |  6 | prq
 row10 | s_15 |   10 | 15 | prq
 row2  | s_7  |    2 |  7 | prq
 row3  | s_8  |    3 |  8 | prq
 row4  | s_9  |    4 |  9 | prq
 row5  | s_10 |    5 | 10 | prq
 row6  | s_11 |    6 | 11 | prq
 row7  | s_12 |    7 | 12 | prq
 row8  | s_13 |    8 | 13 | prq
 row9  | s_14 |    9 | 14 | prq
 row1  | s_6  |    1 |  6 | rc
 row10 | s_15 |   10 | 15 | rc
 row2  | s_7  |    2 |  7 | rc
 row3  | s_8  |    3 |  8 | rc
 row4  | s_9  |    4 |  9 | rc
 row5  | s_10 |    5 | 10 | rc
 row6  | s_11 |    6 | 11 | rc
 row7  | s_12 |    7 | 12 | rc
 row8  | s_13 |    8 | 13 | rc
 row9  | s_14 |    9 | 14 | rc
 row1  | s_6  |    1 |  6 | seq
 row10 | s_15 |   10 | 15 | seq
 row2  | s_7  |    2 |  7 | seq
 row3  | s_8  |    3 |  8 | seq
 row4  | s_9  |    4 |  9 | seq
 row5  | s_10 |    5 | 10 | seq
 row6  | s_11 |    6 | 11 | seq
 row7  | s_12 |    7 | 12 | seq
 row8  | s_13 |    8 | 13 | seq
 row9  | s_14 |    9 | 14 | seq
 row1  | s_6  |    1 |  6 | txt
 row10 | s_15 |   10 | 15 | txt
 row2  | s_7  |    2 |  7 | txt
 row3  | s_8  |    3 |  8 | txt
 row4  | s_9  |    4 |  9 | txt
 row5  | s_10 |    5 | 10 | txt
 row6  | s_11 |    6 | 11 | txt
 row7  | s_12 |    7 | 12 | txt
 row8  | s_13 |    8 | 13 | txt
 row9  | s_14 |    9 | 14 | txt
(50 rows)
