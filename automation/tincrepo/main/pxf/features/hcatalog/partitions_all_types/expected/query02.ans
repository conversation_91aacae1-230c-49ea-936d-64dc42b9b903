-- @description query02 for HCatalog table with multiple partitions - queries for null values
SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE s2 is null ORDER BY s1;
       s1        | s2 | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
-----------------+----+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row12_text_null |    | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE n1 is null ORDER BY s1;
       s1       |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
----------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row13_int_null | s_16 |    | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE d1 is null ORDER BY s1;
        s1         |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
-------------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row14_double_null | s_16 | 11 |    | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE dc1 is null ORDER BY s1;
         s1         |  s2  | n1 | d1 | dc1 |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
--------------------+------+----+----+-----+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row15_decimal_null | s_17 | 12 | 38 |     | 2013-07-24 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE tm is null ORDER BY s1;
          s1          |  s2  | n1 | d1 |         dc1          | tm |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
----------------------+------+----+----+----------------------+----+-----+----------+---+----+------+------------+-------+-----+-----
 row16_timestamp_null | s_16 | 11 | 37 | 0.123456789012345679 |    | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE f is null ORDER BY s1;
       s1        |  s2  | n1 | d1 |         dc1          |         tm          | f |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
-----------------+------+----+----+----------------------+---------------------+---+----------+---+----+------+------------+-------+-----+-----
 row17_real_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 |   | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE bg is null ORDER BY s1;
        s1         |  s2  | n1 | d1 |         dc1          |         tm          |  f  | bg | b | tn | sml  |     dt     |  vc1  | c1  | bin 
-------------------+------+----+----+----------------------+---------------------+-----+----+---+----+------+------------+-------+-----+-----
 row18_bigint_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 |    | f | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE b is null ORDER BY s1;
       s1        |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
-----------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row19_bool_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 |   | 11 | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE tn is null ORDER BY s1;
         s1         |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
--------------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row20_tinyint_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f |    | 1100 | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE sml is null ORDER BY s1;
         s1          |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml |     dt     |  vc1  | c1  | bin 
---------------------+------+----+----+----------------------+---------------------+-----+----------+---+----+-----+------------+-------+-----+-----
 row21_smallint_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 |     | 2015-03-06 | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE dt is null ORDER BY s1;
       s1        |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  | dt |  vc1  | c1  | bin 
-----------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+----+-------+-----+-----
 row22_date_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 |    | abcde | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE vc1 is null ORDER BY s1;
         s1         |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     | vc1 | c1  | bin 
--------------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-----+-----+-----
 row23_varchar_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 |     | ab  | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE c1 is null ORDER BY s1;
       s1        |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1 | bin 
-----------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+----+-----
 row24_char_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f | 11 | 1100 | 2015-03-06 | abcde |    | 31
(1 row)

SELECT * FROM hcatalog.default.hive_many_partitioned_table WHERE bin is null ORDER BY s1;
        s1         |  s2  | n1 | d1 |         dc1          |         tm          |  f  |    bg    | b | tn | sml  |     dt     |  vc1  | c1  | bin 
-------------------+------+----+----+----------------------+---------------------+-----+----------+---+----+------+------------+-------+-----+-----
 row25_binary_null | s_16 | 11 | 37 | 0.123456789012345679 | 2013-07-23 21:00:05 | 7.7 | 23456789 | f |  1 | 1100 | 2015-03-06 | abcde | ab  | 
(1 row)
