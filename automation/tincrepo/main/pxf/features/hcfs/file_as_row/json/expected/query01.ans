-- start_ignore
-- end_ignore
-- start_ignore
-- end_ignore
-- @description query01 tests that a multiline json file returns as a single multiline record in GPDB
--
-- Display on for output consistency between GPDB 5 and 6
\x on
Expanded display is on.
\pset format unaligned
Output format is unaligned.
select * from file_as_row_json;
json_blob|{
  "root":[
    {
      "record":{
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547115253761,
        "id_str":"343136547115253761",
        "text":"text1",
        "source":"<a href=\"http:\/\/twitter.com\/download\/iphone\" rel=\"nofollow\">Twitter for iPhone<\/a>",
        "user":{
          "id":26643566,
          "name":"Spread<PERSON><PERSON>",
          "screen_name":"SpreadButter",
          "location":"Austin, Texas"
        },
        "entities":{
          "hashtags":[
            "tweetCongress",
            "IRS"
          ]
        }
      }
    },
    {
      "record":{
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547123646465,
        "id_str":"343136547123646465",
        "text":"text2",
        "source":"\u003ca href=\"http:\/\/twitter.com\/download\/android\" rel=\"nofollow\"\u003eTwitter for Android\u003c\/a\u003e",
        "user":{
          "id":102904200,
          "id_str":"102904200",
          "name":"Ardianto",
          "screen_name":"patronusdeadly",
          "location":"Bekasi-Surabaya"
        },
        "entities":{
          "hashtags":[

          ]
        }
      }
    },
    {
      "record":{
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547136233472,
        "id_str":"343136547136233472",
        "text":"text3",
        "source":"\u003ca href=\"http:\/\/www.nosecrets-empregos.com.br\/homologa\" rel=\"nofollow\"\u003eVagas NoSecrets\u003c\/a\u003e",
        "user":{
          "id":287819058,
          "id_str":"287819058",
          "name":"No Secrets Empregos",
          "screen_name":"NoSecrets_Vagas",
          "location":""
        },
        "entities":{
          "hashtags":[

          ]
        }
      }
    }
  ]
}
-- Query JSON using JSON functions
\pset format aligned
Output format is aligned.
select
       json_array_elements(json_blob->'root')->'record'->'created_at' as created_at,
       json_array_elements(json_blob->'root')->'record'->'text' as text,
       json_array_elements(json_blob->'root')->'record'->'user'->'name' as username,
       json_array_elements(json_blob->'root')->'record'->'user'->'screen_name' as screen_name,
       json_array_elements(json_blob->'root')->'record'->'user'->'location' as user_location
from file_as_row_json;
-[ RECORD 1 ]-+---------------------------------
created_at    | "Fri Jun 07 22:45:02 +0000 2013"
text          | "text1"
username      | "SpreadButter"
screen_name   | "SpreadButter"
user_location | "Austin, Texas"
-[ RECORD 2 ]-+---------------------------------
created_at    | "Fri Jun 07 22:45:02 +0000 2013"
text          | "text2"
username      | "Ardianto"
screen_name   | "patronusdeadly"
user_location | "Bekasi-Surabaya"
-[ RECORD 3 ]-+---------------------------------
created_at    | "Fri Jun 07 22:45:02 +0000 2013"
text          | "text3"
username      | "No Secrets Empregos"
screen_name   | "NoSecrets_Vagas"
user_location | ""

