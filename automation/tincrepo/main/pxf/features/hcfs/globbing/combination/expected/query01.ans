-- start_ignore
-- end_ignore
-- @description query01 tests a combination of glob patterns
-- use?/*/a.[ch]{lp,xy} will match user/dd/a.hxy but it will not match
-- user/aa/a.c, user/bb/a.cpp, user1/cc/b.hlp
--
select * from hcfs_glob_combination order by name, num;
          name           | num | dub |    longnum    | bool 
-------------------------+-----+-----+---------------+------
 4d user/dd/a.hxy_row_1  |   1 |   1 |  100000000000 | f
 4d user/dd/a.hxy_row_10 |  10 |  10 | 1000000000000 | t
 4d user/dd/a.hxy_row_11 |  11 |  11 | 1100000000000 | f
 4d user/dd/a.hxy_row_12 |  12 |  12 | 1200000000000 | t
 4d user/dd/a.hxy_row_13 |  13 |  13 | 1300000000000 | f
 4d user/dd/a.hxy_row_14 |  14 |  14 | 1400000000000 | t
 4d user/dd/a.hxy_row_15 |  15 |  15 | 1500000000000 | f
 4d user/dd/a.hxy_row_16 |  16 |  16 | 1600000000000 | t
 4d user/dd/a.hxy_row_17 |  17 |  17 | 1700000000000 | f
 4d user/dd/a.hxy_row_18 |  18 |  18 | 1800000000000 | t
 4d user/dd/a.hxy_row_19 |  19 |  19 | 1900000000000 | f
 4d user/dd/a.hxy_row_2  |   2 |   2 |  200000000000 | t
 4d user/dd/a.hxy_row_20 |  20 |  20 | 2000000000000 | t
 4d user/dd/a.hxy_row_3  |   3 |   3 |  300000000000 | f
 4d user/dd/a.hxy_row_4  |   4 |   4 |  400000000000 | t
 4d user/dd/a.hxy_row_5  |   5 |   5 |  500000000000 | f
 4d user/dd/a.hxy_row_6  |   6 |   6 |  600000000000 | t
 4d user/dd/a.hxy_row_7  |   7 |   7 |  700000000000 | f
 4d user/dd/a.hxy_row_8  |   8 |   8 |  800000000000 | t
 4d user/dd/a.hxy_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

