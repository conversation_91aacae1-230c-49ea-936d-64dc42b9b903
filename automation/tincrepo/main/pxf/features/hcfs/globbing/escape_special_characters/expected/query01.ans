-- start_ignore
-- end_ignore
-- @description query01 tests removing (escaping) any special meaning of a character
-- ab\[c.d will match ab[c.d
--
select * from hcfs_glob_escape_special_characters order by name, num;
       name       | num | dub |    longnum    | bool 
------------------+-----+-----+---------------+------
 1a ab[c.d_row_1  |   1 |   1 |  100000000000 | f
 1a ab[c.d_row_10 |  10 |  10 | 1000000000000 | t
 1a ab[c.d_row_11 |  11 |  11 | 1100000000000 | f
 1a ab[c.d_row_12 |  12 |  12 | 1200000000000 | t
 1a ab[c.d_row_13 |  13 |  13 | 1300000000000 | f
 1a ab[c.d_row_14 |  14 |  14 | 1400000000000 | t
 1a ab[c.d_row_15 |  15 |  15 | 1500000000000 | f
 1a ab[c.d_row_16 |  16 |  16 | 1600000000000 | t
 1a ab[c.d_row_17 |  17 |  17 | 1700000000000 | f
 1a ab[c.d_row_18 |  18 |  18 | 1800000000000 | t
 1a ab[c.d_row_19 |  19 |  19 | 1900000000000 | f
 1a ab[c.d_row_2  |   2 |   2 |  200000000000 | t
 1a ab[c.d_row_20 |  20 |  20 | 2000000000000 | t
 1a ab[c.d_row_3  |   3 |   3 |  300000000000 | f
 1a ab[c.d_row_4  |   4 |   4 |  400000000000 | t
 1a ab[c.d_row_5  |   5 |   5 |  500000000000 | f
 1a ab[c.d_row_6  |   6 |   6 |  600000000000 | t
 1a ab[c.d_row_7  |   7 |   7 |  700000000000 | f
 1a ab[c.d_row_8  |   8 |   8 |  800000000000 | t
 1a ab[c.d_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

