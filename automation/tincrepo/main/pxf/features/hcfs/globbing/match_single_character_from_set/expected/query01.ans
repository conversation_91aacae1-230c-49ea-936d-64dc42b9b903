-- start_ignore
-- end_ignore
-- @description query01 tests glob to match a single character from a character set
-- a.[ch]?? will match a.cpp, a.hlp, a.hxy, but it will not match a.c
--
select * from hcfs_glob_match_single_character_from_set order by name, num;
      name       | num | dub |    longnum    | bool 
-----------------+-----+-----+---------------+------
 2b a.cpp_row_1  |   1 |   1 |  100000000000 | f
 2b a.cpp_row_10 |  10 |  10 | 1000000000000 | t
 2b a.cpp_row_11 |  11 |  11 | 1100000000000 | f
 2b a.cpp_row_12 |  12 |  12 | 1200000000000 | t
 2b a.cpp_row_13 |  13 |  13 | 1300000000000 | f
 2b a.cpp_row_14 |  14 |  14 | 1400000000000 | t
 2b a.cpp_row_15 |  15 |  15 | 1500000000000 | f
 2b a.cpp_row_16 |  16 |  16 | 1600000000000 | t
 2b a.cpp_row_17 |  17 |  17 | 1700000000000 | f
 2b a.cpp_row_18 |  18 |  18 | 1800000000000 | t
 2b a.cpp_row_19 |  19 |  19 | 1900000000000 | f
 2b a.cpp_row_2  |   2 |   2 |  200000000000 | t
 2b a.cpp_row_20 |  20 |  20 | 2000000000000 | t
 2b a.cpp_row_3  |   3 |   3 |  300000000000 | f
 2b a.cpp_row_4  |   4 |   4 |  400000000000 | t
 2b a.cpp_row_5  |   5 |   5 |  500000000000 | f
 2b a.cpp_row_6  |   6 |   6 |  600000000000 | t
 2b a.cpp_row_7  |   7 |   7 |  700000000000 | f
 2b a.cpp_row_8  |   8 |   8 |  800000000000 | t
 2b a.cpp_row_9  |   9 |   9 |  900000000000 | f
 3c a.hlp_row_1  |   1 |   1 |  100000000000 | f
 3c a.hlp_row_10 |  10 |  10 | 1000000000000 | t
 3c a.hlp_row_11 |  11 |  11 | 1100000000000 | f
 3c a.hlp_row_12 |  12 |  12 | 1200000000000 | t
 3c a.hlp_row_13 |  13 |  13 | 1300000000000 | f
 3c a.hlp_row_14 |  14 |  14 | 1400000000000 | t
 3c a.hlp_row_15 |  15 |  15 | 1500000000000 | f
 3c a.hlp_row_16 |  16 |  16 | 1600000000000 | t
 3c a.hlp_row_17 |  17 |  17 | 1700000000000 | f
 3c a.hlp_row_18 |  18 |  18 | 1800000000000 | t
 3c a.hlp_row_19 |  19 |  19 | 1900000000000 | f
 3c a.hlp_row_2  |   2 |   2 |  200000000000 | t
 3c a.hlp_row_20 |  20 |  20 | 2000000000000 | t
 3c a.hlp_row_3  |   3 |   3 |  300000000000 | f
 3c a.hlp_row_4  |   4 |   4 |  400000000000 | t
 3c a.hlp_row_5  |   5 |   5 |  500000000000 | f
 3c a.hlp_row_6  |   6 |   6 |  600000000000 | t
 3c a.hlp_row_7  |   7 |   7 |  700000000000 | f
 3c a.hlp_row_8  |   8 |   8 |  800000000000 | t
 3c a.hlp_row_9  |   9 |   9 |  900000000000 | f
 4d a.hxy_row_1  |   1 |   1 |  100000000000 | f
 4d a.hxy_row_10 |  10 |  10 | 1000000000000 | t
 4d a.hxy_row_11 |  11 |  11 | 1100000000000 | f
 4d a.hxy_row_12 |  12 |  12 | 1200000000000 | t
 4d a.hxy_row_13 |  13 |  13 | 1300000000000 | f
 4d a.hxy_row_14 |  14 |  14 | 1400000000000 | t
 4d a.hxy_row_15 |  15 |  15 | 1500000000000 | f
 4d a.hxy_row_16 |  16 |  16 | 1600000000000 | t
 4d a.hxy_row_17 |  17 |  17 | 1700000000000 | f
 4d a.hxy_row_18 |  18 |  18 | 1800000000000 | t
 4d a.hxy_row_19 |  19 |  19 | 1900000000000 | f
 4d a.hxy_row_2  |   2 |   2 |  200000000000 | t
 4d a.hxy_row_20 |  20 |  20 | 2000000000000 | t
 4d a.hxy_row_3  |   3 |   3 |  300000000000 | f
 4d a.hxy_row_4  |   4 |   4 |  400000000000 | t
 4d a.hxy_row_5  |   5 |   5 |  500000000000 | f
 4d a.hxy_row_6  |   6 |   6 |  600000000000 | t
 4d a.hxy_row_7  |   7 |   7 |  700000000000 | f
 4d a.hxy_row_8  |   8 |   8 |  800000000000 | t
 4d a.hxy_row_9  |   9 |   9 |  900000000000 | f
(60 rows)

