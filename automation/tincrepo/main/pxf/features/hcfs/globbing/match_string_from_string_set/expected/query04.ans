-- start_ignore
-- end_ignore
-- @description query04 tests glob to match a string from the given set
-- }{a,b}c will match }bc but it will not match }c
--
select * from hcfs_glob_match_string_from_string_set_4 order by name, num;
     name      | num | dub |    longnum    | bool 
---------------+-----+-----+---------------+------
 1a }bc_row_1  |   1 |   1 |  100000000000 | f
 1a }bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }bc_row_2  |   2 |   2 |  200000000000 | t
 1a }bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }bc_row_3  |   3 |   3 |  300000000000 | f
 1a }bc_row_4  |   4 |   4 |  400000000000 | t
 1a }bc_row_5  |   5 |   5 |  500000000000 | f
 1a }bc_row_6  |   6 |   6 |  600000000000 | t
 1a }bc_row_7  |   7 |   7 |  700000000000 | f
 1a }bc_row_8  |   8 |   8 |  800000000000 | t
 1a }bc_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

-- }{b}c will match }bc but it will not match }c
select * from hcfs_glob_match_string_from_string_set_5 order by name, num;
     name      | num | dub |    longnum    | bool 
---------------+-----+-----+---------------+------
 1a }bc_row_1  |   1 |   1 |  100000000000 | f
 1a }bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }bc_row_2  |   2 |   2 |  200000000000 | t
 1a }bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }bc_row_3  |   3 |   3 |  300000000000 | f
 1a }bc_row_4  |   4 |   4 |  400000000000 | t
 1a }bc_row_5  |   5 |   5 |  500000000000 | f
 1a }bc_row_6  |   6 |   6 |  600000000000 | t
 1a }bc_row_7  |   7 |   7 |  700000000000 | f
 1a }bc_row_8  |   8 |   8 |  800000000000 | t
 1a }bc_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

-- }{}bc will match }bc but it will not match }c
select * from hcfs_glob_match_string_from_string_set_6 order by name, num;
     name      | num | dub |    longnum    | bool 
---------------+-----+-----+---------------+------
 1a }bc_row_1  |   1 |   1 |  100000000000 | f
 1a }bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }bc_row_2  |   2 |   2 |  200000000000 | t
 1a }bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }bc_row_3  |   3 |   3 |  300000000000 | f
 1a }bc_row_4  |   4 |   4 |  400000000000 | t
 1a }bc_row_5  |   5 |   5 |  500000000000 | f
 1a }bc_row_6  |   6 |   6 |  600000000000 | t
 1a }bc_row_7  |   7 |   7 |  700000000000 | f
 1a }bc_row_8  |   8 |   8 |  800000000000 | t
 1a }bc_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

-- }{,}bc will match }bc but it will not match }c
select * from hcfs_glob_match_string_from_string_set_7 order by name, num;
     name      | num | dub |    longnum    | bool 
---------------+-----+-----+---------------+------
 1a }bc_row_1  |   1 |   1 |  100000000000 | f
 1a }bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }bc_row_2  |   2 |   2 |  200000000000 | t
 1a }bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }bc_row_3  |   3 |   3 |  300000000000 | f
 1a }bc_row_4  |   4 |   4 |  400000000000 | t
 1a }bc_row_5  |   5 |   5 |  500000000000 | f
 1a }bc_row_6  |   6 |   6 |  600000000000 | t
 1a }bc_row_7  |   7 |   7 |  700000000000 | f
 1a }bc_row_8  |   8 |   8 |  800000000000 | t
 1a }bc_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

-- }{b,}c will match both }bc and }c
select * from hcfs_glob_match_string_from_string_set_8 order by name, num;
     name      | num | dub |    longnum    | bool 
---------------+-----+-----+---------------+------
 1a }bc_row_1  |   1 |   1 |  100000000000 | f
 1a }bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }bc_row_2  |   2 |   2 |  200000000000 | t
 1a }bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }bc_row_3  |   3 |   3 |  300000000000 | f
 1a }bc_row_4  |   4 |   4 |  400000000000 | t
 1a }bc_row_5  |   5 |   5 |  500000000000 | f
 1a }bc_row_6  |   6 |   6 |  600000000000 | t
 1a }bc_row_7  |   7 |   7 |  700000000000 | f
 1a }bc_row_8  |   8 |   8 |  800000000000 | t
 1a }bc_row_9  |   9 |   9 |  900000000000 | f
 2b }c_row_1   |   1 |   1 |  100000000000 | f
 2b }c_row_10  |  10 |  10 | 1000000000000 | t
 2b }c_row_11  |  11 |  11 | 1100000000000 | f
 2b }c_row_12  |  12 |  12 | 1200000000000 | t
 2b }c_row_13  |  13 |  13 | 1300000000000 | f
 2b }c_row_14  |  14 |  14 | 1400000000000 | t
 2b }c_row_15  |  15 |  15 | 1500000000000 | f
 2b }c_row_16  |  16 |  16 | 1600000000000 | t
 2b }c_row_17  |  17 |  17 | 1700000000000 | f
 2b }c_row_18  |  18 |  18 | 1800000000000 | t
 2b }c_row_19  |  19 |  19 | 1900000000000 | f
 2b }c_row_2   |   2 |   2 |  200000000000 | t
 2b }c_row_20  |  20 |  20 | 2000000000000 | t
 2b }c_row_3   |   3 |   3 |  300000000000 | f
 2b }c_row_4   |   4 |   4 |  400000000000 | t
 2b }c_row_5   |   5 |   5 |  500000000000 | f
 2b }c_row_6   |   6 |   6 |  600000000000 | t
 2b }c_row_7   |   7 |   7 |  700000000000 | f
 2b }c_row_8   |   8 |   8 |  800000000000 | t
 2b }c_row_9   |   9 |   9 |  900000000000 | f
(40 rows)

-- }{,b}c will match both }bc and }c
select * from hcfs_glob_match_string_from_string_set_9 order by name, num;
     name      | num | dub |    longnum    | bool 
---------------+-----+-----+---------------+------
 1a }bc_row_1  |   1 |   1 |  100000000000 | f
 1a }bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }bc_row_2  |   2 |   2 |  200000000000 | t
 1a }bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }bc_row_3  |   3 |   3 |  300000000000 | f
 1a }bc_row_4  |   4 |   4 |  400000000000 | t
 1a }bc_row_5  |   5 |   5 |  500000000000 | f
 1a }bc_row_6  |   6 |   6 |  600000000000 | t
 1a }bc_row_7  |   7 |   7 |  700000000000 | f
 1a }bc_row_8  |   8 |   8 |  800000000000 | t
 1a }bc_row_9  |   9 |   9 |  900000000000 | f
 2b }c_row_1   |   1 |   1 |  100000000000 | f
 2b }c_row_10  |  10 |  10 | 1000000000000 | t
 2b }c_row_11  |  11 |  11 | 1100000000000 | f
 2b }c_row_12  |  12 |  12 | 1200000000000 | t
 2b }c_row_13  |  13 |  13 | 1300000000000 | f
 2b }c_row_14  |  14 |  14 | 1400000000000 | t
 2b }c_row_15  |  15 |  15 | 1500000000000 | f
 2b }c_row_16  |  16 |  16 | 1600000000000 | t
 2b }c_row_17  |  17 |  17 | 1700000000000 | f
 2b }c_row_18  |  18 |  18 | 1800000000000 | t
 2b }c_row_19  |  19 |  19 | 1900000000000 | f
 2b }c_row_2   |   2 |   2 |  200000000000 | t
 2b }c_row_20  |  20 |  20 | 2000000000000 | t
 2b }c_row_3   |   3 |   3 |  300000000000 | f
 2b }c_row_4   |   4 |   4 |  400000000000 | t
 2b }c_row_5   |   5 |   5 |  500000000000 | f
 2b }c_row_6   |   6 |   6 |  600000000000 | t
 2b }c_row_7   |   7 |   7 |  700000000000 | f
 2b }c_row_8   |   8 |   8 |  800000000000 | t
 2b }c_row_9   |   9 |   9 |  900000000000 | f
(40 rows)

-- }{ac,?} will match }c but it will not match }bc
select * from hcfs_glob_match_string_from_string_set_10 order by name, num;
     name     | num | dub |    longnum    | bool 
--------------+-----+-----+---------------+------
 2b }c_row_1  |   1 |   1 |  100000000000 | f
 2b }c_row_10 |  10 |  10 | 1000000000000 | t
 2b }c_row_11 |  11 |  11 | 1100000000000 | f
 2b }c_row_12 |  12 |  12 | 1200000000000 | t
 2b }c_row_13 |  13 |  13 | 1300000000000 | f
 2b }c_row_14 |  14 |  14 | 1400000000000 | t
 2b }c_row_15 |  15 |  15 | 1500000000000 | f
 2b }c_row_16 |  16 |  16 | 1600000000000 | t
 2b }c_row_17 |  17 |  17 | 1700000000000 | f
 2b }c_row_18 |  18 |  18 | 1800000000000 | t
 2b }c_row_19 |  19 |  19 | 1900000000000 | f
 2b }c_row_2  |   2 |   2 |  200000000000 | t
 2b }c_row_20 |  20 |  20 | 2000000000000 | t
 2b }c_row_3  |   3 |   3 |  300000000000 | f
 2b }c_row_4  |   4 |   4 |  400000000000 | t
 2b }c_row_5  |   5 |   5 |  500000000000 | f
 2b }c_row_6  |   6 |   6 |  600000000000 | t
 2b }c_row_7  |   7 |   7 |  700000000000 | f
 2b }c_row_8  |   8 |   8 |  800000000000 | t
 2b }c_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

-- error on ill-formed curly
-- start_matchsubs
--
-- # create a match/subs
--
-- m/Illegal file pattern: Unclosed group near index.*/
-- s/Illegal file pattern: Unclosed group near index.*/Unclosed group near index xxx/
--
-- m/java.util.regex.PatternSyntaxException: Unclosed group near index.*/
-- s/java.util.regex.PatternSyntaxException: Unclosed group near index.*/Unclosed group near index xxx/
--
-- m/DETAIL/
-- s/DETAIL/CONTEXT/
--
-- m/pxf:\/\/(.*)\/pxf_automation_data/
-- s/pxf:\/\/.*match_string_from_string_set_4.*/pxf:\/\/pxf_automation_data?PROFILE=*:text/
--
-- m/CONTEXT:.*line.*/
-- s/line \d* of //g
--
-- end_matchsubs
select * from hcfs_glob_match_string_from_string_set_11 order by name, num;
ERROR:  PXF server error : Illegal file pattern: Unclosed group near index xxx
-- start_ignore
HINT:  Check the PXF logs located in the 'logs-dir' directory on host 'mdw' or 'set client_min_messages=LOG' for additional details.
-- end_ignore
DETAIL:  External table hcfs_glob_match_string_from_string_set_11, file pxf://tmp/pxf_automation_data/match_string_from_string_set_4/}{bc?PROFILE=hdfs:text

-- }\{bc will match }{bc but it will not match }bc
select * from hcfs_glob_match_string_from_string_set_12 order by name, num;
      name      | num | dub |    longnum    | bool 
----------------+-----+-----+---------------+------
 1a }{bc_row_1  |   1 |   1 |  100000000000 | f
 1a }{bc_row_10 |  10 |  10 | 1000000000000 | t
 1a }{bc_row_11 |  11 |  11 | 1100000000000 | f
 1a }{bc_row_12 |  12 |  12 | 1200000000000 | t
 1a }{bc_row_13 |  13 |  13 | 1300000000000 | f
 1a }{bc_row_14 |  14 |  14 | 1400000000000 | t
 1a }{bc_row_15 |  15 |  15 | 1500000000000 | f
 1a }{bc_row_16 |  16 |  16 | 1600000000000 | t
 1a }{bc_row_17 |  17 |  17 | 1700000000000 | f
 1a }{bc_row_18 |  18 |  18 | 1800000000000 | t
 1a }{bc_row_19 |  19 |  19 | 1900000000000 | f
 1a }{bc_row_2  |   2 |   2 |  200000000000 | t
 1a }{bc_row_20 |  20 |  20 | 2000000000000 | t
 1a }{bc_row_3  |   3 |   3 |  300000000000 | f
 1a }{bc_row_4  |   4 |   4 |  400000000000 | t
 1a }{bc_row_5  |   5 |   5 |  500000000000 | f
 1a }{bc_row_6  |   6 |   6 |  600000000000 | t
 1a }{bc_row_7  |   7 |   7 |  700000000000 | f
 1a }{bc_row_8  |   8 |   8 |  800000000000 | t
 1a }{bc_row_9  |   9 |   9 |  900000000000 | f
(20 rows)

