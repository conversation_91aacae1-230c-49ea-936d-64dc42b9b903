-- start_ignore
-- end_ignore
-- @description query03 tests glob to match any zero or more characters for example
-- a*x will match a.txt.x, ax, ab37x, but it will not match bacd
--
select * from hcfs_glob_match_zero_or_more_characters_3 order by name, num;
       name        | num | dub |    longnum    | bool 
-------------------+-----+-----+---------------+------
 1a a.txt.x_row_1  |   1 |   1 |  100000000000 | f
 1a a.txt.x_row_10 |  10 |  10 | 1000000000000 | t
 1a a.txt.x_row_11 |  11 |  11 | 1100000000000 | f
 1a a.txt.x_row_12 |  12 |  12 | 1200000000000 | t
 1a a.txt.x_row_13 |  13 |  13 | 1300000000000 | f
 1a a.txt.x_row_14 |  14 |  14 | 1400000000000 | t
 1a a.txt.x_row_15 |  15 |  15 | 1500000000000 | f
 1a a.txt.x_row_16 |  16 |  16 | 1600000000000 | t
 1a a.txt.x_row_17 |  17 |  17 | 1700000000000 | f
 1a a.txt.x_row_18 |  18 |  18 | 1800000000000 | t
 1a a.txt.x_row_19 |  19 |  19 | 1900000000000 | f
 1a a.txt.x_row_2  |   2 |   2 |  200000000000 | t
 1a a.txt.x_row_20 |  20 |  20 | 2000000000000 | t
 1a a.txt.x_row_3  |   3 |   3 |  300000000000 | f
 1a a.txt.x_row_4  |   4 |   4 |  400000000000 | t
 1a a.txt.x_row_5  |   5 |   5 |  500000000000 | f
 1a a.txt.x_row_6  |   6 |   6 |  600000000000 | t
 1a a.txt.x_row_7  |   7 |   7 |  700000000000 | f
 1a a.txt.x_row_8  |   8 |   8 |  800000000000 | t
 1a a.txt.x_row_9  |   9 |   9 |  900000000000 | f
 2b ax_row_1       |   1 |   1 |  100000000000 | f
 2b ax_row_10      |  10 |  10 | 1000000000000 | t
 2b ax_row_11      |  11 |  11 | 1100000000000 | f
 2b ax_row_12      |  12 |  12 | 1200000000000 | t
 2b ax_row_13      |  13 |  13 | 1300000000000 | f
 2b ax_row_14      |  14 |  14 | 1400000000000 | t
 2b ax_row_15      |  15 |  15 | 1500000000000 | f
 2b ax_row_16      |  16 |  16 | 1600000000000 | t
 2b ax_row_17      |  17 |  17 | 1700000000000 | f
 2b ax_row_18      |  18 |  18 | 1800000000000 | t
 2b ax_row_19      |  19 |  19 | 1900000000000 | f
 2b ax_row_2       |   2 |   2 |  200000000000 | t
 2b ax_row_20      |  20 |  20 | 2000000000000 | t
 2b ax_row_3       |   3 |   3 |  300000000000 | f
 2b ax_row_4       |   4 |   4 |  400000000000 | t
 2b ax_row_5       |   5 |   5 |  500000000000 | f
 2b ax_row_6       |   6 |   6 |  600000000000 | t
 2b ax_row_7       |   7 |   7 |  700000000000 | f
 2b ax_row_8       |   8 |   8 |  800000000000 | t
 2b ax_row_9       |   9 |   9 |  900000000000 | f
 3c ab37x_row_1    |   1 |   1 |  100000000000 | f
 3c ab37x_row_10   |  10 |  10 | 1000000000000 | t
 3c ab37x_row_11   |  11 |  11 | 1100000000000 | f
 3c ab37x_row_12   |  12 |  12 | 1200000000000 | t
 3c ab37x_row_13   |  13 |  13 | 1300000000000 | f
 3c ab37x_row_14   |  14 |  14 | 1400000000000 | t
 3c ab37x_row_15   |  15 |  15 | 1500000000000 | f
 3c ab37x_row_16   |  16 |  16 | 1600000000000 | t
 3c ab37x_row_17   |  17 |  17 | 1700000000000 | f
 3c ab37x_row_18   |  18 |  18 | 1800000000000 | t
 3c ab37x_row_19   |  19 |  19 | 1900000000000 | f
 3c ab37x_row_2    |   2 |   2 |  200000000000 | t
 3c ab37x_row_20   |  20 |  20 | 2000000000000 | t
 3c ab37x_row_3    |   3 |   3 |  300000000000 | f
 3c ab37x_row_4    |   4 |   4 |  400000000000 | t
 3c ab37x_row_5    |   5 |   5 |  500000000000 | f
 3c ab37x_row_6    |   6 |   6 |  600000000000 | t
 3c ab37x_row_7    |   7 |   7 |  700000000000 | f
 3c ab37x_row_8    |   8 |   8 |  800000000000 | t
 3c ab37x_row_9    |   9 |   9 |  900000000000 | f
(60 rows)

