-- @description query01 for PXF HDFS Readable Avro test case for Array of logical types.

set timezone='utc';
SET

\x
Expanded display is on.

select * from array_of_logical_types;
-[ RECORD 1 ]-------------+------------------------------------------------------------------------------------------------------------------------------------------------------
type_uid                  | {67799edc-11b8-11ec-82a8-0242ac130003,d7130d85-cb94-49b1-a722-d3c842f77d01,f8240c3b-7da3-4bde-9253-d9273435bba6,565b7e2a-ea95-41ef-b1fb-0be2e5658024}
type_decnum               | {130.50,0.11,1231.56,0.12}
type_dob                  | {2003-10-20,2024-11-18,1997-05-28,2079-08-23}
type_timemillis           | {04:02:20.334,04:32:28.504,04:21:18.906,02:48:12.387}
type_timemicros           | {04:02:20.334343,04:32:28.508141,04:21:18.906111,02:48:12.387999}
type_timestampmillis      | {"2021-10-21 19:48:24.629+00","2020-01-31 13:23:34.345+00","2020-05-23 19:12:44.123+00","2019-05-23 19:12:44.446+00"}
type_timestampmicros      | {"2021-10-21 19:48:24.629556+00","2020-01-31 13:23:34.345556+00","2020-05-23 19:12:44.123345+00","2019-05-23 19:12:44.446556+00"}
type_localtimestampmicros | {"2021-10-21 19:48:24.629556","2020-01-31 13:23:34.345556","2020-05-23 19:12:44.123345","2019-05-23 19:12:44.446556"}
type_localtimestampmillis | {"2021-10-21 19:48:24.629","2020-01-31 13:23:34.345","2020-05-23 19:12:44.123","2019-05-23 19:12:44.446"}
-[ RECORD 2 ]-------------+------------------------------------------------------------------------------------------------------------------------------------------------------
type_uid                  | {47e2d904-0de3-45e6-8388-36287c445544,3928f462-e193-4dd6-8d42-634c857ff420,41eb1c31-c37d-4ff0-8585-6cb8383a6fd2,1f501214-77df-41d4-958e-467dd8fcb09c}
type_decnum               | {900.45,99.01,99.12,1111.11}
type_dob                  | {2003-10-20,2052-02-23,2033-11-28,2033-02-04}
type_timemillis           | {09:35:21.245,05:32:01.355,03:25:44.999,02:48:22.018}
type_timemicros           | {09:35:21.245123,05:32:01.355334,03:25:44.999789,02:48:22.018567}
type_timestampmillis      | {"1974-10-20 05:35:58.091+00","2021-09-09 18:11:11.222+00","2021-09-09 15:11:11.678+00","2010-10-10 17:10:10.898+00"}
type_timestampmicros      | {"2018-01-01 07:59:44.091553+00","2021-09-09 18:11:11.222123+00","2021-09-09 15:11:11.678444+00","2010-10-10 17:10:10.898556+00"}
type_localtimestampmicros | {"2018-01-01 07:59:44.091553","2021-09-09 18:11:11.222123","2021-09-09 15:11:11.678444","2010-10-10 17:10:10.898556"}
type_localtimestampmillis | {"2018-01-01 07:59:44.091","2021-09-09 18:11:11.222","2021-09-09 15:11:11.678","2010-10-10 17:10:10.898"}

set timezone='America/Los_Angeles';
SET

select * from array_of_logical_types;
-[ RECORD 1 ]-------------+------------------------------------------------------------------------------------------------------------------------------------------------------
type_uid                  | {67799edc-11b8-11ec-82a8-0242ac130003,d7130d85-cb94-49b1-a722-d3c842f77d01,f8240c3b-7da3-4bde-9253-d9273435bba6,565b7e2a-ea95-41ef-b1fb-0be2e5658024}
type_decnum               | {130.50,0.11,1231.56,0.12}
type_dob                  | {2003-10-20,2024-11-18,1997-05-28,2079-08-23}
type_timemillis           | {04:02:20.334,04:32:28.504,04:21:18.906,02:48:12.387}
type_timemicros           | {04:02:20.334343,04:32:28.508141,04:21:18.906111,02:48:12.387999}
type_timestampmillis      | {"2021-10-21 12:48:24.629-07","2020-01-31 05:23:34.345-08","2020-05-23 12:12:44.123-07","2019-05-23 12:12:44.446-07"}
type_timestampmicros      | {"2021-10-21 12:48:24.629556-07","2020-01-31 05:23:34.345556-08","2020-05-23 12:12:44.123345-07","2019-05-23 12:12:44.446556-07"}
type_localtimestampmicros | {"2021-10-21 19:48:24.629556","2020-01-31 13:23:34.345556","2020-05-23 19:12:44.123345","2019-05-23 19:12:44.446556"}
type_localtimestampmillis | {"2021-10-21 19:48:24.629","2020-01-31 13:23:34.345","2020-05-23 19:12:44.123","2019-05-23 19:12:44.446"}
-[ RECORD 2 ]-------------+------------------------------------------------------------------------------------------------------------------------------------------------------
type_uid                  | {47e2d904-0de3-45e6-8388-36287c445544,3928f462-e193-4dd6-8d42-634c857ff420,41eb1c31-c37d-4ff0-8585-6cb8383a6fd2,1f501214-77df-41d4-958e-467dd8fcb09c}
type_decnum               | {900.45,99.01,99.12,1111.11}
type_dob                  | {2003-10-20,2052-02-23,2033-11-28,2033-02-04}
type_timemillis           | {09:35:21.245,05:32:01.355,03:25:44.999,02:48:22.018}
type_timemicros           | {09:35:21.245123,05:32:01.355334,03:25:44.999789,02:48:22.018567}
type_timestampmillis      | {"1974-10-19 22:35:58.091-07","2021-09-09 11:11:11.222-07","2021-09-09 08:11:11.678-07","2010-10-10 10:10:10.898-07"}
type_timestampmicros      | {"2017-12-31 23:59:44.091553-08","2021-09-09 11:11:11.222123-07","2021-09-09 08:11:11.678444-07","2010-10-10 10:10:10.898556-07"}
type_localtimestampmicros | {"2018-01-01 07:59:44.091553","2021-09-09 18:11:11.222123","2021-09-09 15:11:11.678444","2010-10-10 17:10:10.898556"}
type_localtimestampmillis | {"2018-01-01 07:59:44.091","2021-09-09 18:11:11.222","2021-09-09 15:11:11.678","2010-10-10 17:10:10.898"}
