-- @description query01 for PXF HDFS Readable Avro supported array types test cases
SELECT * from avrotest_arrays ORDER BY type_int;
  type_int  | type_int_array | type_double | type_double_array |                  type_string                  | type_string_array | type_float |  type_float_array  |   type_long   |        type_long_array        | type_bytes |       type_bytes_array       | type_boolean | type_boolean_array
------------+----------------+-------------+-------------------+-----------------------------------------------+-------------------+------------+--------------------+---------------+-------------------------------+------------+------------------------------+--------------+--------------------
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 1234       | [\\061\\062\\063,\\141\\142] | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 123        | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 12         | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 1          | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 12         | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 123        | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 1234       | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 12345      | [\\063,\\062]                | f            | [false,false,true]
 -200000002 | [-1,-2]        |        -2.2 | [-0.1,-0.2,-0.3]  | testing all supported types in AvroResolver 2 | [and,arrays,too]  |       -2.2 | [-0.1,0.2,-0.3333] | -200000000002 | [-300000000003,-500000000005] | 1          | [\\063,\\062]                | f            | [false,false,true]
  100000001 | [1,2]          |         1.1 | [0.1,0.2,0.3]     | testing all supported types in AvroResolver 1 | [and,also,arrays] |        1.1 | [0.1,0.2,0.3333]   |  100000000001 | [200000000002,400000000004]   | 12345      | [\\062,\\063]                | t            | [true,false,false]
(10 rows)

SELECT * from avrotest_arrays_gpdb_arrays ORDER BY type_int;
  type_int  | type_int_array | type_double | type_double_array |                  type_string                  | type_string_array | type_float |  type_float_array  |   type_long   |        type_long_array        | type_bytes | type_bytes_array | type_boolean | type_boolean_array
------------+----------------+-------------+-------------------+-----------------------------------------------+-------------------+------------+--------------------+---------------+-------------------------------+------------+------------------+--------------+--------------------
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 1234       | {123,ab}         | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 123        | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 12         | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 1          | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 12         | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 123        | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 1234       | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 12345      | {3,2}            | f            | {f,f,t}
 -200000002 | {-1,-2}        |        -2.2 | {-0.1,-0.2,-0.3}  | testing all supported types in AvroResolver 2 | {and,arrays,too}  |       -2.2 | {-0.1,0.2,-0.3333} | -200000000002 | {-300000000003,-500000000005} | 1          | {3,2}            | f            | {f,f,t}
  100000001 | {1,2}          |         1.1 | {0.1,0.2,0.3}     | testing all supported types in AvroResolver 1 | {and,also,arrays} |        1.1 | {0.1,0.2,0.3333}   |  100000000001 | {200000000002,400000000004}   | 12345      | {2,3}            | t            | {t,f,f}
(10 rows)

SELECT type_int_array, type_double_array, type_string_array, type_float_array, type_long_array, type_bytes_array, type_boolean_array from avrotest_arrays ORDER BY type_int;
 type_int_array | type_double_array | type_string_array |  type_float_array  |        type_long_array        |       type_bytes_array       | type_boolean_array
----------------+-------------------+-------------------+--------------------+-------------------------------+------------------------------+--------------------
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\061\\062\\063,\\141\\142] | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [-1,-2]        | [-0.1,-0.2,-0.3]  | [and,arrays,too]  | [-0.1,0.2,-0.3333] | [-300000000003,-500000000005] | [\\063,\\062]                | [false,false,true]
 [1,2]          | [0.1,0.2,0.3]     | [and,also,arrays] | [0.1,0.2,0.3333]   | [200000000002,400000000004]   | [\\062,\\063]                | [true,false,false]
(10 rows)

SELECT type_int_array, type_double_array, type_string_array, type_float_array, type_long_array, type_bytes_array, type_boolean_array from avrotest_arrays_gpdb_arrays ORDER BY type_int;
 type_int_array | type_double_array | type_string_array |  type_float_array  |        type_long_array        | type_bytes_array | type_boolean_array
----------------+-------------------+-------------------+--------------------+-------------------------------+------------------+--------------------
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {123,ab}         | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {-1,-2}        | {-0.1,-0.2,-0.3}  | {and,arrays,too}  | {-0.1,0.2,-0.3333} | {-300000000003,-500000000005} | {3,2}            | {f,f,t}
 {1,2}          | {0.1,0.2,0.3}     | {and,also,arrays} | {0.1,0.2,0.3333}   | {200000000002,400000000004}   | {2,3}            | {t,f,f}
(10 rows)