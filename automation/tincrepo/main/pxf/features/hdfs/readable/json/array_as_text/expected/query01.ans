-- @description query01 for PXF HDFS Readable Json arrays as text test cases
\pset null 'NIL'
Null display is "NIL".
SELECT * FROM jsontest_array_as_text ORDER BY id;
 id | emp_arr | emp_obj |             num_arr              |     bool_arr      |         str_arr          |        arr_arr         |        obj_arr         |                                                 obj
----+---------+---------+----------------------------------+-------------------+--------------------------+------------------------+------------------------+-----------------------------------------------------------------------------------------------------
  0 | []      | {}      | [null,1,-1.3,1.2345678901234567] | [null,true,false] | [null,"hello","wor\"ld"] | [null,["a","b"],[1,2]] | [null,{"a":1},{"a":2}] | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"x","data":["wow",null]}}}
  1 | []      | {}      | [2,null,-2.3,2.234567890123457]  | [true,null,false] | ["hello",null,"wor\"ld"] | [["a","b"],null,[1,2]] | [{"a":1},null,{"a":2}] | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"y","data":["wow",null]}}}
  2 | []      | {}      | [3,-3.3,null,3.234567890123457]  | [true,false,null] | ["hello","wor\"ld",null] | [["a","b"],[1,2],null] | [{"a":1},{"a":2},null] | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"z","data":["wow",null]}}}
(3 rows)

SELECT * FROM jsontest_array_as_varchar ORDER BY id;
 id | emp_arr | emp_obj |             num_arr              |     bool_arr      |         str_arr          |        arr_arr         |        obj_arr         |                                                 obj
----+---------+---------+----------------------------------+-------------------+--------------------------+------------------------+------------------------+-----------------------------------------------------------------------------------------------------
  0 | []      | {}      | [null,1,-1.3,1.2345678901234567] | [null,true,false] | [null,"hello","wor\"ld"] | [null,["a","b"],[1,2]] | [null,{"a":1},{"a":2}] | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"x","data":["wow",null]}}}
  1 | []      | {}      | [2,null,-2.3,2.234567890123457]  | [true,null,false] | ["hello",null,"wor\"ld"] | [["a","b"],null,[1,2]] | [{"a":1},null,{"a":2}] | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"y","data":["wow",null]}}}
  2 | []      | {}      | [3,-3.3,null,3.234567890123457]  | [true,false,null] | ["hello","wor\"ld",null] | [["a","b"],[1,2],null] | [{"a":1},{"a":2},null] | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"z","data":["wow",null]}}}
(3 rows)

SELECT * FROM jsontest_array_as_bpchar ORDER BY id;
 id | emp_arr |  emp_obj   |                  num_arr                   |     bool_arr      |          str_arr          |          arr_arr          |         obj_arr         |                                                 obj
----+---------+------------+--------------------------------------------+-------------------+---------------------------+---------------------------+-------------------------+------------------------------------------------------------------------------------------------------
  0 | []      | {}         | [null,1,-1.3,1.2345678901234567]           | [null,true,false] | [null,"hello","wor\"ld"]  | [null,["a","b"],[1,2]]    | [null,{"a":1},{"a":2}]  | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"x","data":["wow",null]}}}
  1 | []      | {}         | [2,null,-2.3,2.234567890123457]            | [true,null,false] | ["hello",null,"wor\"ld"]  | [["a","b"],null,[1,2]]    | [{"a":1},null,{"a":2}]  | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"y","data":["wow",null]}}}
  2 | []      | {}         | [3,-3.3,null,3.234567890123457]            | [true,false,null] | ["hello","wor\"ld",null]  | [["a","b"],[1,2],null]    | [{"a":1},{"a":2},null]  | {"level":0,"key":"a","data":{"level":1,"key":"b","data":{"level":2,"key":"z","data":["wow",null]}}}
(3 rows)

SELECT id, ((num_arr::json)->>0)::integer "num", ((bool_arr::json)->>1)::boolean "bool", (str_arr::json)->>2 "str", (arr_arr::json)->>0 "arr",
(obj_arr::json)->>1 "obj", (obj::json)->'data'->'data'->>'key' "key" FROM jsontest_array_as_text ORDER BY id;
 id | num | bool |  str   |    arr    |   obj   | key
----+-----+------+--------+-----------+---------+-----
  0 | NIL | t    | wor"ld | NIL       | {"a":1} | x
  1 |   2 | NIL  | wor"ld | ["a","b"] | NIL     | y
  2 |   3 | f    | NIL    | ["a","b"] | {"a":2} | z
(3 rows)

SELECT * FROM jsontest_array_as_text_projections ORDER BY id;
 id | emp_arr[0] | emp_arr[1] | num_arr[0] | num_arr[1] | num_arr[100] | bool_arr[0] | str_arr[2] | arr_arr[0] | arr_arr[1] | arr_arr[100] | obj_arr[0] | obj_arr[1] | obj_arr[100] | obj_arr[0].a | obj.data.data.data | obj.data.data.data[0] |               obj.data.data
----+------------+------------+------------+------------+--------------+-------------+------------+------------+------------+--------------+------------+------------+--------------+--------------+--------------------+-----------------------+-------------------------------------------
  0 | NIL        | NIL        |        NIL |          1 |          NIL | NIL         | wor"ld     | NIL        | ["a","b"]  | NIL          | NIL        | {"a":1}    | NIL          | NIL          | ["wow",null]       | wow                   | {"level":2,"key":"x","data":["wow",null]}
  1 | NIL        | NIL        |          2 |        NIL |          NIL | t           | wor"ld     | ["a","b"]  | NIL        | NIL          | {"a":1}    | NIL        | NIL          | NIL          | ["wow",null]       | wow                   | {"level":2,"key":"y","data":["wow",null]}
  2 | NIL        | NIL        |          3 |       -3.3 |          NIL | t           | NIL        | ["a","b"]  | [1,2]      | NIL          | {"a":1}    | {"a":2}    | NIL          | NIL          | ["wow",null]       | wow                   | {"level":2,"key":"z","data":["wow",null]}
(3 rows)
