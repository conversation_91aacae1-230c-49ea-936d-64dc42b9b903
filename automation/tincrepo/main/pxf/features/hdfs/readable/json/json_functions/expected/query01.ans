-- @description query01 for PXF HDFS Readable Json Functions test cases
\pset null 'NIL'
Null display is "NIL".
-- This only works in Greenplum 6 since Greenplum 5 does not have json_array_elements_text() function
SELECT id,
       ARRAY(SELECT json_array_elements_text(num_arr::json))::decimal[]  AS numbers,
       ARRAY(SELECT json_array_elements_text(bool_arr::json))::boolean[] AS booleans,
       ARRAY(SELECT json_array_elements_text(str_arr::json))::text[]     AS strings,
       ARRAY(SELECT json_array_elements_text(arr_arr::json))::text[]     AS arrays
FROM jsontest_array_as_text
ORDER BY id;
 id |             numbers              |  booleans  |        strings         |             arrays
----+----------------------------------+------------+------------------------+--------------------------------
  0 | {NULL,1,-1.3,1.2345678901234567} | {NULL,t,f} | {NULL,hello,"wor\"ld"} | {NULL,"[\"a\",\"b\"]","[1,2]"}
  1 | {2,NULL,-2.3,2.234567890123457}  | {t,NULL,f} | {hello,NULL,"wor\"ld"} | {"[\"a\",\"b\"]",NULL,"[1,2]"}
  2 | {3,-3.3,NULL,3.234567890123457}  | {t,f,NULL} | {hello,"wor\"ld",NULL} | {"[\"a\",\"b\"]","[1,2]",NULL}
(3 rows)

