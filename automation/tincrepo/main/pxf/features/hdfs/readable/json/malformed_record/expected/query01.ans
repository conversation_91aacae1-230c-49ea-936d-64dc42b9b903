-- start_matchsubs
-- m/^CONTEXT: */
-- s/^CONTEXT: *//
--
-- m/^DETAIL:*/
-- s/^DETAIL:*//
--
-- end_matchsubs
-- @description query01 for PXF HDFS Readable Json with malformed record test cases
SELECT *
FROM jsontest_malformed_record
ORDER BY id;
ERROR:  error while parsing json record 'Unexpected character (':' (code 58)): was expecting comma to separate Object entries

 at [Source: (String)"{
      "record":
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547123646465,
        "id_str":"343136547123646465",
        "text":"text2",
        "source":"\u003ca href=\"http:\/\/twitter.com\/download\/android\" rel=\"nofollow\"\u003eTwitter for Android\u003c\/a\u003e",
        "user":{
          "id":102904200,
          "id_str":"102904200",
          "name":"Ardiant<PERSON>",
          "screen_name":"patronusdeadly",
          "location":"Bekasi-Surabaya"
        },
 "[truncated 73 chars]; line: 3, column: 22]'. invalid JSON record
{
      "record":
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547123646465,
        "id_str":"343136547123646465",
        "text":"text2",
        "source":"\u003ca href=\"http:\/\/twitter.com\/download\/android\" rel=\"nofollow\"\u003eTwitter for Android\u003c\/a\u003e",
        "user":{
          "id":102904200,
          "id_str":"102904200",
          "name":"Ardianto",
          "screen_name":"patronusdeadly",
          "location":"Bekasi-Surabaya"
        },
        "entities":{
          "hashtags":[

          ]
        }
      }
External table jsontest_malformed_record
SELECT *
FROM jsontest_malformed_record
WHERE id IS NULL
ORDER BY id;
ERROR:  error while parsing json record 'Unexpected character (':' (code 58)): was expecting comma to separate Object entries

 at [Source: (String)"{
      "record":
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547123646465,
        "id_str":"343136547123646465",
        "text":"text2",
        "source":"\u003ca href=\"http:\/\/twitter.com\/download\/android\" rel=\"nofollow\"\u003eTwitter for Android\u003c\/a\u003e",
        "user":{
          "id":102904200,
          "id_str":"102904200",
          "name":"Ardianto",
          "screen_name":"patronusdeadly",
          "location":"Bekasi-Surabaya"
        },
 "[truncated 73 chars]; line: 3, column: 22]'. invalid JSON record
{
      "record":
        "created_at":"Fri Jun 07 22:45:02 +0000 2013",
        "id":343136547123646465,
        "id_str":"343136547123646465",
        "text":"text2",
        "source":"\u003ca href=\"http:\/\/twitter.com\/download\/android\" rel=\"nofollow\"\u003eTwitter for Android\u003c\/a\u003e",
        "user":{
          "id":102904200,
          "id_str":"102904200",
          "name":"Ardianto",
          "screen_name":"patronusdeadly",
          "location":"Bekasi-Surabaya"
        },
        "entities":{
          "hashtags":[

          ]
        }
      }
External table jsontest_malformed_record
