-- start_ignore
-- end_ignore
-- @description query01 for PXF HDFS Readable Text Small Data cases reading multiple bzip2 compressed files
SELECT * from pxf_hdfs_small_data_bzip2 ORDER BY name;
   name    | num | dub |    longnum    | bool 
-----------+-----+-----+---------------+------
 aa_row_1  |   1 |   1 |  100000000000 | f
 aa_row_10 |  10 |  10 | 1000000000000 | t
 aa_row_2  |   2 |   2 |  200000000000 | t
 aa_row_3  |   3 |   3 |  300000000000 | f
 aa_row_4  |   4 |   4 |  400000000000 | t
 aa_row_5  |   5 |   5 |  500000000000 | f
 aa_row_6  |   6 |   6 |  600000000000 | t
 aa_row_7  |   7 |   7 |  700000000000 | f
 aa_row_8  |   8 |   8 |  800000000000 | t
 aa_row_9  |   9 |   9 |  900000000000 | f
 bb_row_1  |   1 |   1 |  100000000000 | f
 bb_row_10 |  10 |  10 | 1000000000000 | t
 bb_row_2  |   2 |   2 |  200000000000 | t
 bb_row_3  |   3 |   3 |  300000000000 | f
 bb_row_4  |   4 |   4 |  400000000000 | t
 bb_row_5  |   5 |   5 |  500000000000 | f
 bb_row_6  |   6 |   6 |  600000000000 | t
 bb_row_7  |   7 |   7 |  700000000000 | f
 bb_row_8  |   8 |   8 |  800000000000 | t
 bb_row_9  |   9 |   9 |  900000000000 | f
 cc_row_1  |   1 |   1 |  100000000000 | f
 cc_row_10 |  10 |  10 | 1000000000000 | t
 cc_row_2  |   2 |   2 |  200000000000 | t
 cc_row_3  |   3 |   3 |  300000000000 | f
 cc_row_4  |   4 |   4 |  400000000000 | t
 cc_row_5  |   5 |   5 |  500000000000 | f
 cc_row_6  |   6 |   6 |  600000000000 | t
 cc_row_7  |   7 |   7 |  700000000000 | f
 cc_row_8  |   8 |   8 |  800000000000 | t
 cc_row_9  |   9 |   9 |  900000000000 | f
 dd_row_1  |   1 |   1 |  100000000000 | f
 dd_row_10 |  10 |  10 | 1000000000000 | t
 dd_row_2  |   2 |   2 |  200000000000 | t
 dd_row_3  |   3 |   3 |  300000000000 | f
 dd_row_4  |   4 |   4 |  400000000000 | t
 dd_row_5  |   5 |   5 |  500000000000 | f
 dd_row_6  |   6 |   6 |  600000000000 | t
 dd_row_7  |   7 |   7 |  700000000000 | f
 dd_row_8  |   8 |   8 |  800000000000 | t
 dd_row_9  |   9 |   9 |  900000000000 | f
 ee_row_1  |   1 |   1 |  100000000000 | f
 ee_row_10 |  10 |  10 | 1000000000000 | t
 ee_row_2  |   2 |   2 |  200000000000 | t
 ee_row_3  |   3 |   3 |  300000000000 | f
 ee_row_4  |   4 |   4 |  400000000000 | t
 ee_row_5  |   5 |   5 |  500000000000 | f
 ee_row_6  |   6 |   6 |  600000000000 | t
 ee_row_7  |   7 |   7 |  700000000000 | f
 ee_row_8  |   8 |   8 |  800000000000 | t
 ee_row_9  |   9 |   9 |  900000000000 | f
 ff_row_1  |   1 |   1 |  100000000000 | f
 ff_row_10 |  10 |  10 | 1000000000000 | t
 ff_row_2  |   2 |   2 |  200000000000 | t
 ff_row_3  |   3 |   3 |  300000000000 | f
 ff_row_4  |   4 |   4 |  400000000000 | t
 ff_row_5  |   5 |   5 |  500000000000 | f
 ff_row_6  |   6 |   6 |  600000000000 | t
 ff_row_7  |   7 |   7 |  700000000000 | f
 ff_row_8  |   8 |   8 |  800000000000 | t
 ff_row_9  |   9 |   9 |  900000000000 | f
 gg_row_1  |   1 |   1 |  100000000000 | f
 gg_row_10 |  10 |  10 | 1000000000000 | t
 gg_row_2  |   2 |   2 |  200000000000 | t
 gg_row_3  |   3 |   3 |  300000000000 | f
 gg_row_4  |   4 |   4 |  400000000000 | t
 gg_row_5  |   5 |   5 |  500000000000 | f
 gg_row_6  |   6 |   6 |  600000000000 | t
 gg_row_7  |   7 |   7 |  700000000000 | f
 gg_row_8  |   8 |   8 |  800000000000 | t
 gg_row_9  |   9 |   9 |  900000000000 | f
 hh_row_1  |   1 |   1 |  100000000000 | f
 hh_row_10 |  10 |  10 | 1000000000000 | t
 hh_row_2  |   2 |   2 |  200000000000 | t
 hh_row_3  |   3 |   3 |  300000000000 | f
 hh_row_4  |   4 |   4 |  400000000000 | t
 hh_row_5  |   5 |   5 |  500000000000 | f
 hh_row_6  |   6 |   6 |  600000000000 | t
 hh_row_7  |   7 |   7 |  700000000000 | f
 hh_row_8  |   8 |   8 |  800000000000 | t
 hh_row_9  |   9 |   9 |  900000000000 | f
 ii_row_1  |   1 |   1 |  100000000000 | f
 ii_row_10 |  10 |  10 | 1000000000000 | t
 ii_row_2  |   2 |   2 |  200000000000 | t
 ii_row_3  |   3 |   3 |  300000000000 | f
 ii_row_4  |   4 |   4 |  400000000000 | t
 ii_row_5  |   5 |   5 |  500000000000 | f
 ii_row_6  |   6 |   6 |  600000000000 | t
 ii_row_7  |   7 |   7 |  700000000000 | f
 ii_row_8  |   8 |   8 |  800000000000 | t
 ii_row_9  |   9 |   9 |  900000000000 | f
 jj_row_1  |   1 |   1 |  100000000000 | f
 jj_row_10 |  10 |  10 | 1000000000000 | t
 jj_row_2  |   2 |   2 |  200000000000 | t
 jj_row_3  |   3 |   3 |  300000000000 | f
 jj_row_4  |   4 |   4 |  400000000000 | t
 jj_row_5  |   5 |   5 |  500000000000 | f
 jj_row_6  |   6 |   6 |  600000000000 | t
 jj_row_7  |   7 |   7 |  700000000000 | f
 jj_row_8  |   8 |   8 |  800000000000 | t
 jj_row_9  |   9 |   9 |  900000000000 | f
(100 rows)

